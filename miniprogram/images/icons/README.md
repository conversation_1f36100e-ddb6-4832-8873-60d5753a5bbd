# 图标文件说明

## 需要添加的图标文件

请在此目录下添加以下图标文件：

1. 客户图标
   - customer.png - 未选中状态
   - customer-active.png - 选中状态

2. 记录图标
   - record.png - 未选中状态
   - record-active.png - 选中状态

3. 产品图标
   - product.png - 未选中状态
   - product-active.png - 选中状态

## 图标要求

- 建议尺寸：40px × 40px 或 80px × 80px（适配高分辨率屏幕）
- 格式：PNG格式（透明背景）
- 风格：iOS风格，简洁、扁平化设计
- 颜色：
  - 未选中状态：灰色 (#8a8a8a)
  - 选中状态：蓝色 (#007AFF)

## 图标资源

您可以从以下网站获取适合的图标：

- [Iconfont](https://www.iconfont.cn/)
- [Flaticon](https://www.flaticon.com/)
- [Icons8](https://icons8.com/)

## 添加图标后的操作

添加完图标后，需要修改 app.json 文件，恢复图标配置：

```json
"tabBar": {
  "color": "#8a8a8a",
  "selectedColor": "#007AFF",
  "backgroundColor": "#ffffff",
  "borderStyle": "white",
  "list": [
    {
      "pagePath": "pages/customer/list/index",
      "text": "客户",
      "iconPath": "images/icons/customer.png",
      "selectedIconPath": "images/icons/customer-active.png"
    },
    {
      "pagePath": "pages/record/add/index",
      "text": "记录",
      "iconPath": "images/icons/record.png",
      "selectedIconPath": "images/icons/record-active.png"
    },
    {
      "pagePath": "pages/product/list/index",
      "text": "产品",
      "iconPath": "images/icons/product.png",
      "selectedIconPath": "images/icons/product-active.png"
    }
  ]
}
```
