<view class="container">
  <form bindsubmit="submitForm">
    <!-- 基本信息 -->
    <view class="form-section-title">基本信息</view>
    <view class="ios-form-group">
      <view class="ios-form-item">
        <text class="ios-form-label">产品名称</text>
        <input class="ios-form-input" name="name" placeholder="请输入产品名称" value="{{product.name}}" />
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">价格</text>
        <input class="ios-form-input" name="price" type="digit" placeholder="请输入价格" value="{{product.price}}" />
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">产品类型</text>
        <picker bindchange="bindTypeChange" value="{{typeIndex}}" range="{{productTypes}}" range-key="label">
          <view class="ios-form-input">
            {{productTypes[typeIndex].label}}
          </view>
        </picker>
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">分类</text>
        <picker bindchange="bindCategoryChange" value="{{categoryIndex}}" range="{{categories}}">
          <view class="ios-form-input {{categoryIndex >= 0 ? '' : 'placeholder'}}">
            {{categoryIndex >= 0 ? categories[categoryIndex] : '请选择分类'}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 会员卡特有信息 -->
    <block wx:if="{{showMembershipFields}}">
      <view class="form-section-title">会员卡信息</view>
      <view class="ios-form-group">
        <view class="ios-form-item">
          <text class="ios-form-label">有效期(月)</text>
          <input class="ios-form-input" type="number" placeholder="请输入有效期(月)" value="{{membershipInfo.duration}}" bindinput="onDurationInput" />
        </view>
        <view class="ios-form-item">
          <text class="ios-form-label">训练次数</text>
          <input class="ios-form-input" type="number" placeholder="请输入包含的训练次数" value="{{membershipInfo.trainingCount}}" bindinput="onTrainingCountInput" />
        </view>
        <view class="ios-form-item">
          <text class="ios-form-label">会员等级</text>
          <picker bindchange="bindLevelChange" value="{{levelIndex}}" range="{{levelOptions}}" range-key="label">
            <view class="ios-form-input">
              {{levelOptions[levelIndex].label}}
            </view>
          </picker>
        </view>
      </view>

      <view class="form-section-title">会员特权</view>
      <view class="ios-form-group">
        <view class="ios-form-item">
          <text class="ios-form-label">折扣率</text>
          <input class="ios-form-input" type="digit" placeholder="请输入折扣率(如0.9表示9折)" value="{{membershipInfo.privileges.discount}}" bindinput="onDiscountInput" />
        </view>
        <view class="ios-form-item">
          <text class="ios-form-label">积分倍数</text>
          <input class="ios-form-input" type="digit" placeholder="请输入积分倍数(如1.5)" value="{{membershipInfo.privileges.pointsMultiplier}}" bindinput="onPointsMultiplierInput" />
        </view>
        <view class="ios-form-item">
          <text class="ios-form-label">免运费</text>
          <switch checked="{{membershipInfo.privileges.freeShipping}}" bindchange="switchFreeShipping" />
        </view>
        <view class="ios-form-item">
          <text class="ios-form-label">优先服务</text>
          <switch checked="{{membershipInfo.privileges.priorityService}}" bindchange="switchPriorityService" />
        </view>
      </view>

      <!-- 包含产品 -->
      <view class="form-section-title">包含产品</view>
      <view class="ios-form-group">
        <view class="ios-form-item" bindtap="showProductSelectorModal">
          <text class="ios-form-label">添加产品</text>
          <view class="ios-form-input add-product">
            <text>点击添加产品</text>
            <text class="add-icon">+</text>
          </view>
        </view>

        <!-- 已选产品列表 -->
        <view class="selected-products" wx:if="{{selectedProducts.length > 0}}">
          <view class="selected-product" wx:for="{{selectedProducts}}" wx:key="productId">
            <view class="product-info">
              <text class="product-name">{{item.name}}</text>
              <text class="product-price">¥{{item.price}}</text>
            </view>
            <view class="product-count">
              <text class="count-label">数量：</text>
              <view class="count-control">
                <text class="count-btn" bindtap="decreaseProductCount" data-index="{{index}}">-</text>
                <text class="count-value">{{item.count}}</text>
                <text class="count-btn" bindtap="increaseProductCount" data-index="{{index}}">+</text>
              </view>
            </view>
            <view class="product-remove" bindtap="removeProduct" data-index="{{index}}">
              <text class="remove-icon">×</text>
            </view>
          </view>
        </view>
      </view>
    </block>

    <!-- 其他信息 -->
    <view class="form-section-title">其他信息</view>
    <view class="ios-form-group">
      <view class="ios-form-item notes-item">
        <text class="ios-form-label">描述</text>
        <textarea class="ios-form-textarea" name="description" placeholder="请输入产品描述" value="{{product.description}}" />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="form-actions">
      <button class="ios-btn-secondary" bindtap="cancelForm">取消</button>
      <button class="ios-btn" form-type="submit">保存</button>
    </view>
  </form>
</view>

<!-- 产品选择器模态框 -->
<view class="modal-mask" wx:if="{{showProductSelector}}" bindtap="hideProductSelector"></view>
<view class="modal-container" wx:if="{{showProductSelector}}">
  <view class="modal-header">
    <text class="modal-title">选择产品</text>
    <text class="modal-close" bindtap="hideProductSelector">×</text>
  </view>

  <view class="modal-search">
    <input class="search-input" placeholder="搜索产品" value="{{productKeyword}}" bindinput="onProductSearchInput" />
    <button class="search-btn" bindtap="searchProducts">搜索</button>
  </view>

  <scroll-view class="modal-content" scroll-y>
    <view class="product-list">
      <view class="product-item" wx:for="{{products}}" wx:key="_id" bindtap="selectProduct" data-product="{{item}}">
        <view class="product-name">{{item.name}}</view>
        <view class="product-meta">
          <text class="product-price">¥{{item.price}}</text>
          <text class="product-category">{{item.category}}</text>
        </view>
      </view>
      <view class="empty-tip" wx:if="{{products.length === 0}}">
        <text>没有找到产品</text>
      </view>
    </view>
  </scroll-view>
</view>
