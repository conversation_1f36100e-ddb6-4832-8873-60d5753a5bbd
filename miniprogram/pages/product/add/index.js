// 添加产品页
const app = getApp()

Page({
  data: {
    productId: '',
    product: {},
    // 分类相关
    normalCategories: [],
    membershipCategories: [],
    categories: [],
    categoryOptions: [], // 分类选项对象数组
    categoryIndex: -1,
    isEdit: false,
    // 产品类型相关
    productTypes: [
      { value: 'normal', label: '普通产品' },
      { value: 'membership', label: '会员卡' }
    ],
    typeIndex: 0,
    showMembershipFields: false,
    // 会员卡特有字段
    membershipInfo: {
      duration: 1,
      trainingCount: 0,
      level: 'basic',
      privileges: {
        discount: 0.9, // 默认9折
        pointsMultiplier: 1.0,
        freeShipping: false,
        priorityService: false
      },
      benefits: [],
      includedProducts: []
    },
    // 会员等级选项
    levelOptions: [
      { value: 'basic', label: '基础卡' },
      { value: 'premium', label: '高级卡' },
      { value: 'annual', label: '年度卡' },
      { value: 'custom', label: '定制卡' }
    ],
    levelIndex: 0,
    // 包含产品相关
    showProductSelector: false,
    products: [],
    selectedProducts: [],
    productKeyword: ''
  },

  onLoad: function (options) {
    // 加载分类数据
    this.loadCategories(() => {
      // 检查是否是会员卡类型
      if (options.type === 'membership') {
        const typeIndex = this.data.productTypes.findIndex(item => item.value === 'membership');

        // 设置会员卡分类选项
        this.setData({
          typeIndex: typeIndex >= 0 ? typeIndex : 1,
          showMembershipFields: true,
          categories: this.data.membershipCategories
        });

        // 同步会员等级和分类
        setTimeout(() => {
          this.syncLevelAndCategory();
        }, 100);

        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: '添加会员卡'
        });
      }

      if (options.id) {
        // 编辑模式
        this.setData({
          productId: options.id,
          isEdit: true
        })

        // 根据类型设置导航栏标题
        if (options.type === 'membership') {
          wx.setNavigationBarTitle({
            title: '编辑会员卡'
          });
        } else {
          wx.setNavigationBarTitle({
            title: '编辑产品'
          });
        }

        this.loadProductDetail()
      }
    });
  },

  // 加载分类数据
  loadCategories: function(callback) {
    wx.showLoading({
      title: '加载中...',
    });

    wx.cloud.callFunction({
      name: 'categoryService',
      data: {
        action: 'getCategoryList',
        data: {
          page: 1,
          pageSize: 100
        }
      }
    }).then(res => {
      const { success, data } = res.result;

      if (success && data) {
        // 分离普通产品分类和会员卡分类
        const normalCats = data.filter(item => item.type === 'normal').map(item => item.name);
        const membershipCats = data.filter(item => item.type === 'membership').map(item => item.name);

        this.setData({
          normalCategories: normalCats,
          membershipCategories: membershipCats,
          categories: this.data.showMembershipFields ? membershipCats : normalCats,
          categoryOptions: data
        });
      }
    }).catch(err => {
      console.error('获取分类列表失败', err);
      wx.showToast({
        title: '获取分类列表失败',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
      callback && callback();
    });
  },

  // 加载产品详情
  loadProductDetail: function () {
    const { productId } = this.data

    wx.showLoading({
      title: '加载中...',
    })

    wx.cloud.callFunction({
      name: 'productService',
      data: {
        action: 'getProductDetail',
        data: {
          productId
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success && data) {
        // 处理产品类型
        let typeIndex = 0;
        let showMembershipFields = false;
        let membershipInfo = this.data.membershipInfo;
        let levelIndex = 0;
        let categories = this.data.normalCategories;

        if (data.type === 'membership') {
          typeIndex = this.data.productTypes.findIndex(item => item.value === 'membership');
          showMembershipFields = true;
          categories = this.data.membershipCategories;

          // 处理会员卡特有信息
          if (data.membershipInfo) {
            membershipInfo = data.membershipInfo;

            // 设置会员等级索引
            if (membershipInfo.level) {
              const index = this.data.levelOptions.findIndex(item => item.value === membershipInfo.level);
              if (index >= 0) {
                levelIndex = index;
              }
            }

            // 处理包含的产品
            if (data.membershipInfo.includedProducts && data.membershipInfo.includedProducts.length > 0) {
              this.setData({
                selectedProducts: data.membershipInfo.includedProducts
              });
            }
          }
        }

        // 设置分类索引
        const categoryIndex = categories.findIndex(item => item === data.category);

        this.setData({
          product: data,
          categories,
          categoryIndex: categoryIndex >= 0 ? categoryIndex : -1,
          typeIndex: typeIndex >= 0 ? typeIndex : 0,
          showMembershipFields,
          membershipInfo,
          levelIndex
        })
      } else {
        wx.showToast({
          title: '获取产品详情失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取产品详情失败', err)
      wx.showToast({
        title: '获取产品详情失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 分类选择器变化
  bindCategoryChange: function (e) {
    this.setData({
      categoryIndex: e.detail.value
    })

    // 如果选择了会员卡分类，自动设置产品类型为会员卡
    if (this.data.categories[e.detail.value] === '会员卡') {
      const typeIndex = this.data.productTypes.findIndex(item => item.value === 'membership');
      if (typeIndex >= 0) {
        this.setData({
          typeIndex,
          showMembershipFields: true
        });
      }
    }
  },

  // 产品类型选择器变化
  bindTypeChange: function (e) {
    const typeIndex = e.detail.value;
    const showMembershipFields = this.data.productTypes[typeIndex].value === 'membership';

    // 根据产品类型切换分类选项
    const categories = showMembershipFields ?
      this.data.membershipCategories :
      this.data.normalCategories;

    // 重置分类索引
    const categoryIndex = -1;

    this.setData({
      typeIndex,
      showMembershipFields,
      categories,
      categoryIndex
    });

    // 如果选择会员卡类型，自动设置会员等级与分类对应
    if (showMembershipFields) {
      this.syncLevelAndCategory();
    }
  },

  // 同步会员等级和分类
  syncLevelAndCategory: function() {
    const { levelIndex, levelOptions, membershipCategories } = this.data;
    const level = levelOptions[levelIndex].value;

    // 根据会员等级找到对应的分类索引
    let categoryIndex = -1;

    switch(level) {
      case 'basic':
        categoryIndex = membershipCategories.findIndex(item => item === '基础会员卡');
        break;
      case 'premium':
        categoryIndex = membershipCategories.findIndex(item => item === '高级会员卡');
        break;
      case 'annual':
        categoryIndex = membershipCategories.findIndex(item => item === '年度会员卡');
        break;
      case 'custom':
        categoryIndex = membershipCategories.findIndex(item => item === '定制会员卡');
        break;
    }

    if (categoryIndex >= 0) {
      this.setData({ categoryIndex });
    }
  },

  // 会员等级选择器变化
  bindLevelChange: function (e) {
    const levelIndex = e.detail.value;

    this.setData({
      levelIndex,
      'membershipInfo.level': this.data.levelOptions[levelIndex].value
    });

    // 同步更新分类
    if (this.data.showMembershipFields) {
      this.syncLevelAndCategory();
    }
  },

  // 会员折扣输入
  onDiscountInput: function (e) {
    this.setData({
      'membershipInfo.privileges.discount': parseFloat(e.detail.value) || 1.0
    });
  },

  // 积分倍数输入
  onPointsMultiplierInput: function (e) {
    this.setData({
      'membershipInfo.privileges.pointsMultiplier': parseFloat(e.detail.value) || 1.0
    });
  },

  // 免运费开关
  switchFreeShipping: function (e) {
    this.setData({
      'membershipInfo.privileges.freeShipping': e.detail.value
    });
  },

  // 优先服务开关
  switchPriorityService: function (e) {
    this.setData({
      'membershipInfo.privileges.priorityService': e.detail.value
    });
  },

  // 会员有效期输入
  onDurationInput: function (e) {
    this.setData({
      'membershipInfo.duration': parseInt(e.detail.value) || 1
    });
  },

  // 训练次数输入
  onTrainingCountInput: function (e) {
    this.setData({
      'membershipInfo.trainingCount': parseInt(e.detail.value) || 0
    });
  },

  // 显示产品选择器
  showProductSelectorModal: function() {
    this.setData({
      showProductSelector: true
    });
    this.loadProductList();
  },

  // 隐藏产品选择器
  hideProductSelector: function() {
    this.setData({
      showProductSelector: false
    });
  },

  // 加载产品列表
  loadProductList: function() {
    wx.showLoading({
      title: '加载中...',
    });

    wx.cloud.callFunction({
      name: 'productService',
      data: {
        action: 'getProductList',
        data: {
          keyword: this.data.productKeyword,
          type: 'normal', // 只加载普通产品
          page: 1,
          pageSize: 100
        }
      }
    }).then(res => {
      const { success, data } = res.result;

      if (success) {
        this.setData({
          products: data
        });
      } else {
        wx.showToast({
          title: '获取产品列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取产品列表失败', err);
      wx.showToast({
        title: '获取产品列表失败',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },

  // 产品搜索输入
  onProductSearchInput: function(e) {
    this.setData({
      productKeyword: e.detail.value
    });
  },

  // 搜索产品
  searchProducts: function() {
    this.loadProductList();
  },

  // 选择产品
  selectProduct: function(e) {
    const product = e.currentTarget.dataset.product;
    const { selectedProducts } = this.data;

    // 检查是否已选择该产品
    const index = selectedProducts.findIndex(item => item.productId === product._id);

    if (index >= 0) {
      // 已选择，增加数量
      const newSelectedProducts = [...selectedProducts];
      newSelectedProducts[index].count += 1;

      this.setData({
        selectedProducts: newSelectedProducts
      });
    } else {
      // 未选择，添加到已选列表
      this.setData({
        selectedProducts: [...selectedProducts, {
          productId: product._id,
          name: product.name,
          price: product.price,
          count: 1
        }]
      });
    }

    this.hideProductSelector();
    this.updateIncludedProducts();
  },

  // 增加产品数量
  increaseProductCount: function(e) {
    const index = e.currentTarget.dataset.index;
    const { selectedProducts } = this.data;

    const newSelectedProducts = [...selectedProducts];
    newSelectedProducts[index].count += 1;

    this.setData({
      selectedProducts: newSelectedProducts
    });

    this.updateIncludedProducts();
  },

  // 减少产品数量
  decreaseProductCount: function(e) {
    const index = e.currentTarget.dataset.index;
    const { selectedProducts } = this.data;

    const newSelectedProducts = [...selectedProducts];
    if (newSelectedProducts[index].count > 1) {
      newSelectedProducts[index].count -= 1;

      this.setData({
        selectedProducts: newSelectedProducts
      });

      this.updateIncludedProducts();
    }
  },

  // 移除产品
  removeProduct: function(e) {
    const index = e.currentTarget.dataset.index;
    const { selectedProducts } = this.data;

    const newSelectedProducts = [...selectedProducts];
    newSelectedProducts.splice(index, 1);

    this.setData({
      selectedProducts: newSelectedProducts
    });

    this.updateIncludedProducts();
  },

  // 更新会员卡包含的产品
  updateIncludedProducts: function() {
    const { selectedProducts } = this.data;

    this.setData({
      'membershipInfo.includedProducts': selectedProducts
    });
  },

  // 提交表单
  submitForm: function (e) {
    const formData = e.detail.value
    const {
      categoryIndex,
      categories,
      productId,
      isEdit,
      typeIndex,
      productTypes,
      showMembershipFields,
      membershipInfo
    } = this.data

    // 表单验证
    if (!formData.name) {
      wx.showToast({
        title: '请输入产品名称',
        icon: 'none'
      })
      return
    }

    if (!formData.price) {
      wx.showToast({
        title: '请输入价格',
        icon: 'none'
      })
      return
    }

    if (categoryIndex < 0) {
      wx.showToast({
        title: '请选择分类',
        icon: 'none'
      })
      return
    }

    // 会员卡特有字段验证
    if (showMembershipFields) {
      if (!membershipInfo.duration || membershipInfo.duration < 1) {
        wx.showToast({
          title: '请输入有效的会员有效期',
          icon: 'none'
        })
        return
      }
    }

    wx.showLoading({
      title: isEdit ? '更新中...' : '添加中...',
    })

    // 获取选中的分类对象
    const selectedCategory = this.data.categoryOptions.find(
      item => item.name === categories[categoryIndex]
    );

    // 构建数据
    const data = {
      name: formData.name,
      price: parseFloat(formData.price),
      category: categories[categoryIndex],
      categoryId: selectedCategory ? selectedCategory._id : '',
      description: formData.description || '',
      type: productTypes[typeIndex].value
    }

    // 如果是会员卡类型，添加会员卡特有信息
    if (showMembershipFields) {
      data.membershipInfo = membershipInfo
    }

    if (isEdit) {
      // 更新产品
      data._id = productId

      wx.cloud.callFunction({
        name: 'productService',
        data: {
          action: 'updateProduct',
          data
        }
      }).then(res => {
        const { success, message } = res.result

        if (success) {
          wx.showToast({
            title: '更新成功',
          })
          setTimeout(() => {
            this.safeNavigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: message || '更新失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        console.error('更新产品失败', err)
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        })
      }).finally(() => {
        wx.hideLoading()
      })
    } else {
      // 添加产品
      wx.cloud.callFunction({
        name: 'productService',
        data: {
          action: 'addProduct',
          data
        }
      }).then(res => {
        const { success, message } = res.result

        if (success) {
          wx.showToast({
            title: '添加成功',
          })
          setTimeout(() => {
            this.safeNavigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: message || '添加失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        console.error('添加产品失败', err)
        wx.showToast({
          title: '添加失败',
          icon: 'none'
        })
      }).finally(() => {
        wx.hideLoading()
      })
    }
  },

  // 取消表单
  cancelForm: function () {
    this.safeNavigateBack()
  },

  // 安全的返回导航
  safeNavigateBack: function() {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      wx.navigateBack()
    } else {
      // 如果只有一个页面，跳转到产品列表页
      wx.redirectTo({
        url: '/pages/product/list/index'
      })
    }
  }
})
