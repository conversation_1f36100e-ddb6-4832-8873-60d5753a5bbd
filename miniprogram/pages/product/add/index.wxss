/* 添加产品页样式 */
.container {
  padding: 16px;
}

.form-section-title {
  font-size: 14px;
  color: #8E8E93;
  margin: 20px 0 10px 10px;
}

.ios-form-textarea {
  width: 100%;
  height: 100px;
  font-size: 16px;
  padding: 10px 0;
}

.notes-item {
  height: auto;
  min-height: 100px;
}

.placeholder {
  color: #C7C7CC;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  margin-bottom: 30px;
}

.form-actions .ios-btn,
.form-actions .ios-btn-secondary {
  width: 48%;
}

/* 添加产品按钮 */
.add-product {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #007AFF;
}

.add-icon {
  font-size: 20px;
  font-weight: bold;
}

/* 已选产品列表 */
.selected-products {
  margin-top: 10px;
  border-top: 1px solid #E5E5EA;
}

.selected-product {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #E5E5EA;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 16px;
  color: #000;
  margin-bottom: 4px;
}

.product-price {
  font-size: 14px;
  color: #8E8E93;
}

.product-count {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.count-label {
  font-size: 14px;
  color: #8E8E93;
  margin-right: 5px;
}

.count-control {
  display: flex;
  align-items: center;
}

.count-btn {
  width: 24px;
  height: 24px;
  line-height: 22px;
  text-align: center;
  border: 1px solid #E5E5EA;
  border-radius: 12px;
  font-size: 16px;
  color: #007AFF;
}

.count-value {
  margin: 0 8px;
  font-size: 16px;
  min-width: 20px;
  text-align: center;
}

.product-remove {
  padding: 5px 10px;
}

.remove-icon {
  font-size: 20px;
  color: #FF3B30;
}

/* 模态框样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #E5E5EA;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  font-size: 24px;
  color: #8E8E93;
}

.modal-search {
  display: flex;
  padding: 10px 15px;
  border-bottom: 1px solid #E5E5EA;
}

.search-input {
  flex: 1;
  height: 36px;
  border: 1px solid #E5E5EA;
  border-radius: 18px;
  padding: 0 15px;
  font-size: 14px;
  margin-right: 10px;
}

.search-btn {
  height: 36px;
  line-height: 36px;
  padding: 0 15px;
  font-size: 14px;
  background-color: #007AFF;
  color: #fff;
  border-radius: 18px;
}

.modal-content {
  flex: 1;
  max-height: 50vh;
}

.product-list {
  padding: 0 15px;
}

.product-item {
  padding: 15px 0;
  border-bottom: 1px solid #E5E5EA;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}

.product-category {
  font-size: 14px;
  color: #8E8E93;
}

.empty-tip {
  padding: 30px 0;
  text-align: center;
  color: #8E8E93;
}
