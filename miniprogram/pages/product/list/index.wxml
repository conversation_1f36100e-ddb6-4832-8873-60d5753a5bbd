<view class="container">
  <!-- 搜索框 -->
  <view class="ios-search">
    <text class="ios-search-icon">🔍</text>
    <input class="ios-search-input" placeholder="搜索产品" bindinput="onSearchInput" value="{{keyword}}" confirm-type="search" bindconfirm="onSearch" />
  </view>

  <!-- 分类筛选 -->
  <view class="category-filter">
    <view class="category-item {{currentCategory === '' ? 'active' : ''}}" bindtap="filterByCategory" data-category="">
      全部
    </view>
    <view class="category-item {{currentCategory === '眼镜' ? 'active' : ''}}" bindtap="filterByCategory" data-category="眼镜">
      眼镜
    </view>
    <view class="category-item {{currentCategory === '训练工具' ? 'active' : ''}}" bindtap="filterByCategory" data-category="训练工具">
      训练工具
    </view>
    <view class="category-item {{currentCategory === '其他' ? 'active' : ''}}" bindtap="filterByCategory" data-category="其他">
      其他
    </view>
  </view>

  <!-- 产品列表 -->
  <view class="product-list">
    <block wx:if="{{products.length > 0}}">
      <view class="ios-card product-card" wx:for="{{products}}" wx:key="_id">
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-price">¥{{item.price}}</view>
          <view class="product-category">
            <text class="ios-tag ios-tag-blue">{{item.category}}</text>
          </view>
          <view class="product-description" wx:if="{{item.description}}">
            {{item.description}}
          </view>
        </view>
        <view class="product-actions">
          <button class="ios-btn-secondary edit-btn" bindtap="editProduct" data-id="{{item._id}}">编辑</button>
          <button class="ios-btn delete-btn" bindtap="deleteProduct" data-id="{{item._id}}">删除</button>
        </view>
      </view>
    </block>
    <view class="empty-list" wx:else>
      <text>暂无产品数据</text>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <text bindtap="loadMore">加载更多</text>
  </view>

  <!-- 添加产品按钮 -->
  <view class="add-btn" bindtap="goToAddProduct">
    <text>+</text>
  </view>

  <!-- 分类管理按钮 -->
  <view class="category-btn" bindtap="goToCategoryManage">
    <text>分类</text>
  </view>
</view>
