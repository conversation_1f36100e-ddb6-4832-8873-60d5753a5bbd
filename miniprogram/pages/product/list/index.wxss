/* 产品列表页样式 */
.container {
  padding: 16px;
}

/* 分类筛选 */
.category-filter {
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
}

.category-item {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  font-size: 14px;
  color: #8E8E93;
  border-bottom: 2px solid transparent;
}

.category-item.active {
  color: #007AFF;
  border-bottom: 2px solid #007AFF;
}

/* 产品卡片 */
.product-card {
  margin-bottom: 15px;
}

.product-info {
  margin-bottom: 15px;
}

.product-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.product-price {
  font-size: 16px;
  color: #FF3B30;
  font-weight: 500;
  margin-bottom: 5px;
}

.product-category {
  margin-bottom: 5px;
}

.product-description {
  font-size: 14px;
  color: #8E8E93;
  margin-top: 5px;
}

.product-actions {
  display: flex;
  justify-content: space-between;
}

.edit-btn, .delete-btn {
  width: 48%;
  margin: 0;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  font-weight: 400;
}

.edit-btn {
  background-color: #F2F2F7;
  color: #007AFF;
  border: 1px solid #E5E5EA;
}

.delete-btn {
  background-color: #FF3B30;
  color: #fff;
  border: none;
}

.empty-list {
  padding: 30px 0;
  text-align: center;
  color: #8E8E93;
}

.load-more {
  text-align: center;
  padding: 15px 0;
  color: #007AFF;
  font-size: 14px;
}

/* 使用全局统一的 FAB 样式 */
