// 产品列表页
const app = getApp()

Page({
  data: {
    products: [],
    keyword: '',
    currentCategory: '',
    page: 1,
    pageSize: 10,
    total: 0,
    hasMore: false,
    loading: false
  },

  onLoad: function (options) {
    this.loadProducts()
  },

  onPullDownRefresh: function () {
    this.setData({
      products: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadProducts(() => {
        wx.stopPullDownRefresh()
      })
    })
  },

  // 加载产品列表
  loadProducts: function (callback) {
    const { keyword, currentCategory, page, pageSize } = this.data

    if (this.data.loading) return

    this.setData({ loading: true })

    wx.cloud.callFunction({
      name: 'productService',
      data: {
        action: 'getProductList',
        data: {
          keyword,
          category: currentCategory,
          page,
          pageSize
        }
      }
    }).then(res => {
      const { success, data, total } = res.result

      if (success) {
        this.setData({
          products: page === 1 ? data : [...this.data.products, ...data],
          total,
          hasMore: this.data.products.length + data.length < total
        })
      } else {
        wx.showToast({
          title: '获取产品列表失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取产品列表失败', err)
      wx.showToast({
        title: '获取产品列表失败',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({ loading: false })
      callback && callback()
    })
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      keyword: e.detail.value
    })
  },

  // 搜索确认
  onSearch: function () {
    this.setData({
      products: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadProducts()
    })
  },

  // 分类筛选
  filterByCategory: function (e) {
    const category = e.currentTarget.dataset.category

    this.setData({
      currentCategory: category,
      products: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadProducts()
    })
  },

  // 加载更多
  loadMore: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      }, () => {
        this.loadProducts()
      })
    }
  },

  // 编辑产品
  editProduct: function (e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product/add/index?id=${productId}`
    })
  },

  // 删除产品
  deleteProduct: function (e) {
    const productId = e.currentTarget.dataset.id

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个产品吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          })

          wx.cloud.callFunction({
            name: 'productService',
            data: {
              action: 'deleteProduct',
              data: {
                productId
              }
            }
          }).then(res => {
            const { success, message } = res.result

            if (success) {
              wx.showToast({
                title: '删除成功',
              })

              // 刷新列表
              this.setData({
                products: this.data.products.filter(item => item._id !== productId)
              })
            } else {
              wx.showToast({
                title: message || '删除失败',
                icon: 'none'
              })
            }
          }).catch(err => {
            console.error('删除产品失败', err)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }).finally(() => {
            wx.hideLoading()
          })
        }
      }
    })
  },

  // 跳转到添加产品页
  goToAddProduct: function () {
    wx.navigateTo({
      url: '/pages/product/add/index'
    })
  },

  // 跳转到分类管理页
  goToCategoryManage: function () {
    wx.navigateTo({
      url: '/pages/category/list/index'
    })
  },

  onShareAppMessage: function () {
    return {
      title: '产品管理',
      path: '/pages/product/list/index'
    }
  }
})
