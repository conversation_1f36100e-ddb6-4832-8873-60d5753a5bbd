<view class="container">
  <form bindsubmit="submitForm">
    <!-- 基本信息 -->
    <view class="form-section-title">基本信息</view>
    <view class="ios-form-group">
      <view class="ios-form-item">
        <text class="ios-form-label">姓名</text>
        <input class="ios-form-input" name="name" placeholder="请输入客户姓名" value="{{customer.name}}" />
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">电话</text>
        <input class="ios-form-input" name="phone" type="number" placeholder="请输入电话号码" value="{{customer.phone}}" />
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">加入时间</text>
        <picker mode="date" value="{{joinDate}}" bindchange="bindJoinDateChange">
          <view class="ios-form-input {{joinDate ? '' : 'placeholder'}}">
            {{joinDate || '请选择加入时间'}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 会员信息 -->
    <view class="form-section-title">会员信息</view>
    <view class="ios-form-group">
      <view class="ios-form-item">
        <text class="ios-form-label">会员卡</text>
        <picker bindchange="bindCardChange" value="{{cardIndex}}" range="{{cards}}" range-key="name">
          <view class="ios-form-input {{cardIndex >= 0 ? '' : 'placeholder'}}">
            {{cardIndex >= 0 ? cards[cardIndex].name : '请选择会员卡'}}
          </view>
        </picker>
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">会员有效期</text>
        <picker mode="date" value="{{memberExpireDate}}" bindchange="bindMemberExpireDateChange">
          <view class="ios-form-input {{memberExpireDate ? '' : 'placeholder'}}">
            {{memberExpireDate || '请选择会员有效期'}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 其他信息 -->
    <view class="form-section-title">其他信息</view>
    <view class="ios-form-group">
      <view class="ios-form-item notes-item">
        <textarea class="ios-form-textarea" name="notes" placeholder="请输入备注信息" value="{{customer.notes}}" />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="form-actions">
      <button class="ios-btn-secondary" bindtap="cancelForm">取消</button>
      <button class="ios-btn" form-type="submit">保存</button>
    </view>
  </form>
</view>
