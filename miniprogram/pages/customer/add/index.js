// 添加客户页
const app = getApp()

Page({
  data: {
    customerId: '',
    customer: {},
    joinDate: '',
    memberExpireDate: '',
    isEdit: false,
    // 会员卡相关
    cards: [],
    selectedCardId: '',
    cardIndex: -1
  },

  onLoad: function (options) {
    if (options.id) {
      // 编辑模式
      this.setData({
        customerId: options.id,
        isEdit: true
      })
      wx.setNavigationBarTitle({
        title: '编辑客户'
      })
      this.loadCustomerDetail()
    } else {
      // 添加模式
      const now = new Date()
      const joinDate = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`

      this.setData({
        joinDate
      })
    }

    // 加载会员卡列表
    this.loadMembershipCards()
  },

  // 加载会员卡列表
  loadMembershipCards: function() {
    wx.cloud.callFunction({
      name: 'membershipService',
      data: {
        action: 'getCardList',
        data: {
          page: 1,
          pageSize: 100
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success && data && data.length > 0) {
        this.setData({
          cards: data
        })
      }
    }).catch(err => {
      console.error('获取会员卡列表失败', err)
    })
  },

  // 加载客户详情
  loadCustomerDetail: function () {
    const { customerId } = this.data

    wx.showLoading({
      title: '加载中...',
    })

    wx.cloud.callFunction({
      name: 'customerService',
      data: {
        action: 'getCustomerDetail',
        data: {
          customerId
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success && data) {
        // 处理日期格式
        let joinDate = ''
        let memberExpireDate = ''

        if (data.joinDate) {
          const date = new Date(data.joinDate)
          joinDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }

        if (data.memberExpireDate) {
          const date = new Date(data.memberExpireDate)
          memberExpireDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }

        // 设置选中的会员卡
        let cardIndex = -1
        if (data.membershipCardId && this.data.cards.length > 0) {
          cardIndex = this.data.cards.findIndex(card => card._id === data.membershipCardId)
        }

        this.setData({
          customer: data,
          joinDate,
          memberExpireDate,
          selectedCardId: data.membershipCardId || '',
          cardIndex: cardIndex
        })
      } else {
        wx.showToast({
          title: '获取客户详情失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取客户详情失败', err)
      wx.showToast({
        title: '获取客户详情失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 加入时间选择器变化
  bindJoinDateChange: function (e) {
    this.setData({
      joinDate: e.detail.value
    })
  },

  // 会员有效期选择器变化
  bindMemberExpireDateChange: function (e) {
    this.setData({
      memberExpireDate: e.detail.value
    })
  },

  // 会员卡选择器变化
  bindCardChange: function (e) {
    const index = e.detail.value
    const cardId = this.data.cards[index]._id

    this.setData({
      cardIndex: index,
      selectedCardId: cardId
    })
  },

  // 提交表单
  submitForm: function (e) {
    const formData = e.detail.value
    const { joinDate, memberExpireDate, customerId, isEdit, selectedCardId } = this.data

    // 表单验证
    if (!formData.name) {
      wx.showToast({
        title: '请输入客户姓名',
        icon: 'none'
      })
      return
    }

    if (!formData.phone) {
      wx.showToast({
        title: '请输入电话号码',
        icon: 'none'
      })
      return
    }

    if (!joinDate) {
      wx.showToast({
        title: '请选择加入时间',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: isEdit ? '更新中...' : '添加中...',
    })

    // 构建数据
    const data = {
      name: formData.name,
      phone: formData.phone,
      joinDate: new Date(joinDate),
      notes: formData.notes || ''
    }

    if (memberExpireDate) {
      data.memberExpireDate = new Date(memberExpireDate)
    }

    // 添加会员卡ID
    if (selectedCardId) {
      data.membershipCardId = selectedCardId
    }

    if (isEdit) {
      // 更新客户
      data._id = customerId

      wx.cloud.callFunction({
        name: 'customerService',
        data: {
          action: 'updateCustomer',
          data
        }
      }).then(res => {
        const { success, message } = res.result

        if (success) {
          wx.showToast({
            title: '更新成功',
          })
          setTimeout(() => {
            this.safeNavigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: message || '更新失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        console.error('更新客户失败', err)
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        })
      }).finally(() => {
        wx.hideLoading()
      })
    } else {
      // 添加客户
      wx.cloud.callFunction({
        name: 'customerService',
        data: {
          action: 'addCustomer',
          data
        }
      }).then(res => {
        const { success, message } = res.result

        if (success) {
          wx.showToast({
            title: '添加成功',
          })
          setTimeout(() => {
            this.safeNavigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: message || '添加失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        console.error('添加客户失败', err)
        wx.showToast({
          title: '添加失败',
          icon: 'none'
        })
      }).finally(() => {
        wx.hideLoading()
      })
    }
  },

  // 取消表单
  cancelForm: function () {
    this.safeNavigateBack()
  },

  // 安全的返回导航
  safeNavigateBack: function() {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      wx.navigateBack()
    } else {
      // 如果只有一个页面，跳转到客户列表页
      wx.redirectTo({
        url: '/pages/customer/list/index'
      })
    }
  }
})
