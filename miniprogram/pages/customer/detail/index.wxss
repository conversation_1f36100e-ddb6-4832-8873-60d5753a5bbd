/* 客户详情页样式 */
.container {
  padding: 16px;
  padding-bottom: 80px;
}

/* 客户信息卡片 */
.customer-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.avatar-text {
  font-size: 24px;
  color: #007AFF;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.customer-basic-info {
  flex: 1;
  margin-left: 15px;
}

.customer-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.customer-phone {
  font-size: 14px;
  color: #8E8E93;
}

.customer-status {
  display: flex;
  flex-direction: column;
}

.customer-status .ios-tag {
  margin-bottom: 5px;
}

.customer-detail-info {
  margin-top: 15px;
}

.detail-item {
  display: flex;
  margin-bottom: 10px;
}

.detail-label {
  width: 100px;
  color: #8E8E93;
  font-size: 14px;
}

.detail-value {
  flex: 1;
  font-size: 14px;
}

/* 会员卡信息 */
.membership-card {
  margin-bottom: 20px;
}

.membership-info {
  padding: 0 16px 16px;
}

.membership-name {
  font-size: 18px;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 5px;
}

.membership-level {
  font-size: 14px;
  color: #8E8E93;
  margin-bottom: 10px;
}

.membership-meta {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.membership-status {
  font-size: 14px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 10px;
}

.membership-status.valid {
  background-color: #E5F9E0;
  color: #34C759;
}

.membership-status.invalid {
  background-color: #FFE5E5;
  color: #FF3B30;
}

.membership-expire {
  font-size: 14px;
  color: #8E8E93;
}

.membership-details {
  background-color: #F2F2F7;
  border-radius: 8px;
  padding: 12px;
}

.membership-detail-item {
  display: flex;
  margin-bottom: 8px;
}

.membership-detail-item:last-child {
  margin-bottom: 0;
}

/* 会员特权样式 */
.privileges-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
}

.privilege-item {
  background-color: #E5F2FF;
  border-radius: 4px;
  padding: 4px 8px;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #007AFF;
}

.empty-membership {
  padding: 20px 0;
  text-align: center;
  color: #8E8E93;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-membership text {
  margin-bottom: 10px;
}

.ios-btn-small {
  background-color: #007AFF;
  color: #fff;
  border-radius: 8px;
  font-size: 14px;
  padding: 6px 12px;
  line-height: 1.5;
}

/* 数据概览卡片 */
.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
}

.data-grid {
  display: flex;
  justify-content: space-between;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
}

.data-value {
  font-size: 24px;
  font-weight: 600;
  color: #007AFF;
  margin-bottom: 5px;
}

.data-label {
  font-size: 14px;
  color: #8E8E93;
}

/* 记录卡片 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

/* 使用全局统一的卡片内添加按钮样式 */



.record-info {
  width: 100%;
}

.record-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}

.record-meta {
  font-size: 14px;
  color: #8E8E93;
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.record-price {
  color: #FF3B30;
  font-weight: 500;
}

.record-content {
  font-size: 14px;
  color: #3A3A3C;
  margin-top: 5px;
}

.empty-list {
  padding: 20px 0;
  text-align: center;
  color: #8E8E93;
}

.view-more {
  text-align: center;
  padding: 10px 0;
  color: #007AFF;
  font-size: 14px;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
}

.bottom-actions .ios-btn,
.bottom-actions .ios-btn-secondary {
  width: 48%;
  margin: 0;
}
