<view class="container">
  <!-- 客户基本信息 -->
  <view class="ios-card customer-info-card">
    <view class="customer-header">
      <view class="ios-avatar">
        <text class="avatar-text">{{customer.name[0]}}</text>
      </view>
      <view class="customer-basic-info">
        <view class="customer-name">{{customer.name}}</view>
        <view class="customer-phone">{{customer.phone}}</view>
      </view>
      <view class="customer-status">
        <text class="ios-tag ios-tag-blue" wx:if="{{isNewCustomer}}">新客户</text>
        <text class="ios-tag ios-tag-green" wx:if="{{isMemberValid}}">会员有效</text>
        <text class="ios-tag ios-tag-orange" wx:if="{{!isMemberValid}}">会员过期</text>
      </view>
    </view>

    <view class="ios-divider"></view>

    <view class="customer-detail-info">
      <view class="detail-item">
        <text class="detail-label">加入时间</text>
        <text class="detail-value">{{joinDate}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">会员有效期</text>
        <text class="detail-value">{{memberExpireDate}}</text>
      </view>
      <view class="detail-item" wx:if="{{customer.notes}}">
        <text class="detail-label">备注</text>
        <text class="detail-value">{{customer.notes}}</text>
      </view>
    </view>
  </view>

  <!-- 会员卡信息 -->
  <view class="ios-card membership-card">
    <view class="card-header">
      <view class="card-title">会员卡信息</view>
      <view class="ios-card-add-btn" bindtap="goToPurchaseCard">
        <text>+</text>
      </view>
    </view>

    <block wx:if="{{membershipInfo && membershipInfo.isMember && membershipInfo.card}}">
      <view class="membership-info">
        <view class="membership-name">{{membershipInfo.card.name}}</view>
        <view class="membership-level">{{membershipInfo.level || '普通会员'}}</view>
        <view class="membership-meta">
          <text class="membership-status valid">有效</text>
          <text class="membership-expire">剩余{{membershipInfo.remainingDays}}天</text>
        </view>
        <view class="membership-details">
          <view class="membership-detail-item">
            <text class="detail-label">剩余训练次数：</text>
            <text class="detail-value">{{membershipInfo.remainingTrainingCount || 0}}次</text>
          </view>
          <view class="membership-detail-item">
            <text class="detail-label">购买日期：</text>
            <text class="detail-value">{{purchaseDate || '未知'}}</text>
          </view>

          <!-- 会员特权 -->
          <view class="membership-detail-item" wx:if="{{membershipInfo.privileges}}">
            <text class="detail-label">会员特权：</text>
            <view class="privileges-list">
              <text class="privilege-item" wx:if="{{membershipInfo.privileges.discount < 1}}">
                {{membershipInfo.privileges.discount * 10}}折优惠
              </text>
              <text class="privilege-item" wx:if="{{membershipInfo.privileges.pointsMultiplier > 1}}">
                {{membershipInfo.privileges.pointsMultiplier}}倍积分
              </text>
              <text class="privilege-item" wx:if="{{membershipInfo.privileges.freeShipping}}">
                免运费
              </text>
              <text class="privilege-item" wx:if="{{membershipInfo.privileges.priorityService}}">
                优先服务
              </text>
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-membership" wx:else>
      <text>暂无会员卡信息</text>
      <button class="ios-btn-small" bindtap="goToPurchaseCard">购买会员卡</button>
    </view>
  </view>

  <!-- 数据概览 -->
  <view class="ios-card data-overview-card">
    <view class="card-title">数据概览</view>

    <view class="data-grid">
      <view class="data-item">
        <text class="data-value">{{trainingCount || 0}}</text>
        <text class="data-label">训练次数</text>
      </view>
      <view class="data-item">
        <text class="data-value">{{purchaseCount || 0}}</text>
        <text class="data-label">购买次数</text>
      </view>
      <view class="data-item">
        <text class="data-value">{{totalAmount || 0}}</text>
        <text class="data-label">消费金额</text>
      </view>
    </view>
  </view>

  <!-- 训练记录 -->
  <view class="ios-card records-card">
    <view class="card-header">
      <view class="card-title">训练记录</view>
      <view class="ios-card-add-btn" bindtap="goToAddRecord" data-type="training">
        <text>+</text>
      </view>
    </view>

    <view class="ios-list">
      <block wx:if="{{trainingRecords.length > 0}}">
        <view class="ios-list-item" wx:for="{{trainingRecords}}" wx:key="_id">
          <view class="record-info">
            <view class="record-title">{{item.type === 'eye' ? '眼睛训练' : '视觉训练'}}</view>
            <view class="record-meta">
              <text>{{item.date}}</text>
              <text wx:if="{{item.duration}}">时长: {{item.duration}}分钟</text>
            </view>
            <view class="record-content" wx:if="{{item.content}}">
              {{item.content}}
            </view>
          </view>
        </view>
      </block>
      <view class="empty-list" wx:else>
        <text>暂无训练记录</text>
      </view>
    </view>

    <view class="view-more" wx:if="{{trainingRecords.length > 0}}" bindtap="viewMoreTrainingRecords">
      查看更多
    </view>
  </view>

  <!-- 购买记录 -->
  <view class="ios-card records-card">
    <view class="card-header">
      <view class="card-title">购买记录</view>
      <view class="ios-card-add-btn" bindtap="goToAddRecord" data-type="purchase">
        <text>+</text>
      </view>
    </view>

    <view class="ios-list">
      <block wx:if="{{purchaseRecords.length > 0}}">
        <view class="ios-list-item" wx:for="{{purchaseRecords}}" wx:key="_id">
          <view class="record-info">
            <view class="record-title">{{item.product ? item.product.name : '未知产品'}}</view>
            <view class="record-meta">
              <text>{{item.date}}</text>
              <text class="record-price">¥{{item.amount}}</text>
            </view>
            <view class="record-content" wx:if="{{item.remark}}">
              {{item.remark}}
            </view>
          </view>
        </view>
      </block>
      <view class="empty-list" wx:else>
        <text>暂无购买记录</text>
      </view>
    </view>

    <view class="view-more" wx:if="{{purchaseRecords.length > 0}}" bindtap="viewMorePurchaseRecords">
      查看更多
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="ios-btn-secondary" bindtap="editCustomer">编辑客户</button>
    <button class="ios-btn" bindtap="callCustomer">拨打电话</button>
  </view>
</view>
