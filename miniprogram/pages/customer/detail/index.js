// 客户详情页
const app = getApp()

Page({
  data: {
    customerId: '',
    customer: {},
    trainingRecords: [],
    purchaseRecords: [],
    isNewCustomer: false,
    isMemberValid: false,
    joinDate: '',
    memberExpireDate: '',
    trainingCount: 0,
    purchaseCount: 0,
    totalAmount: 0,
    membershipInfo: null,
    purchaseDate: ''
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        customerId: options.id
      })
      this.loadCustomerDetail()
    } else {
      wx.showToast({
        title: '客户ID不存在',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onPullDownRefresh: function () {
    this.loadCustomerDetail(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载客户详情
  loadCustomerDetail: function (callback) {
    const { customerId } = this.data

    wx.showLoading({
      title: '加载中...',
    })

    wx.cloud.callFunction({
      name: 'customerService',
      data: {
        action: 'getCustomerDetail',
        data: {
          customerId
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success && data) {
        // 处理日期格式
        let joinDate = '未设置'
        let memberExpireDate = '未设置'
        let purchaseDate = '未设置'

        if (data.joinDate) {
          const date = new Date(data.joinDate)
          joinDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }

        if (data.memberExpireDate) {
          const date = new Date(data.memberExpireDate)
          memberExpireDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }

        if (data.purchaseDate) {
          const date = new Date(data.purchaseDate)
          purchaseDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }

        // 判断是否为新客户（3个月内）
        let isNewCustomer = false
        if (data.joinDate) {
          const joinDate = new Date(data.joinDate).getTime()
          const threeMonthsAgo = new Date()
          threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)
          isNewCustomer = joinDate > threeMonthsAgo.getTime()
        }

        // 判断会员是否有效
        let isMemberValid = false
        if (data.memberExpireDate) {
          const expireDate = new Date(data.memberExpireDate).getTime()
          const now = new Date().getTime()
          isMemberValid = expireDate > now
        }

        // 处理训练记录日期
        const trainingRecords = data.trainingRecords.map(record => {
          if (record.createTime) {
            const date = new Date(record.createTime)
            record.date = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
          }
          return record
        })

        // 处理购买记录日期
        const purchaseRecords = data.purchaseRecords.map(record => {
          if (record.createTime) {
            const date = new Date(record.createTime)
            record.date = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
          }
          return record
        })

        // 计算统计数据
        const trainingCount = data.trainingRecords.length
        const purchaseCount = data.purchaseRecords.length
        const totalAmount = data.purchaseRecords.reduce((sum, record) => sum + (record.amount || 0), 0)

        this.setData({
          customer: data,
          trainingRecords,
          purchaseRecords,
          isNewCustomer,
          isMemberValid,
          joinDate,
          memberExpireDate,
          purchaseDate,
          trainingCount,
          purchaseCount,
          totalAmount
        })

        // 获取会员卡信息
        this.loadMembershipInfo()
      } else {
        wx.showToast({
          title: '获取客户详情失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取客户详情失败', err)
      wx.showToast({
        title: '获取客户详情失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
      callback && callback()
    })
  },

  // 查看更多训练记录
  viewMoreTrainingRecords: function () {
    // 这里可以跳转到训练记录列表页
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 查看更多购买记录
  viewMorePurchaseRecords: function () {
    // 这里可以跳转到购买记录列表页
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 跳转到添加记录页
  goToAddRecord: function (e) {
    console.log('goToAddRecord 被调用', e)
    const type = e.currentTarget.dataset.type
    console.log('记录类型:', type)
    console.log('客户ID:', this.data.customerId)

    if (!this.data.customerId) {
      wx.showToast({
        title: '客户ID不存在',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/record/add/index?customerId=${this.data.customerId}&type=${type}`
    })
  },

  // 编辑客户
  editCustomer: function () {
    wx.navigateTo({
      url: `/pages/customer/add/index?id=${this.data.customerId}`
    })
  },

  // 加载会员卡信息
  loadMembershipInfo: function () {
    const { customerId } = this.data

    wx.cloud.callFunction({
      name: 'customerService',
      data: {
        action: 'getMembershipInfo',
        data: {
          customerId
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success) {
        // 处理会员卡购买日期
        let purchaseDate = '未设置'
        if (data.purchaseDate) {
          const date = new Date(data.purchaseDate)
          purchaseDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }

        this.setData({
          membershipInfo: data,
          purchaseDate
        })
      }
    }).catch(err => {
      console.error('获取会员卡信息失败', err)
    })
  },

  // 跳转到购买会员卡页
  goToPurchaseCard: function () {
    wx.navigateTo({
      url: `/pages/membership/purchase/index?customerId=${this.data.customerId}`
    })
  },

  // 拨打电话
  callCustomer: function () {
    const { phone } = this.data.customer
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone
      })
    } else {
      wx.showToast({
        title: '电话号码不存在',
        icon: 'none'
      })
    }
  },

  onShareAppMessage: function () {
    return {
      title: `${this.data.customer.name}的客户详情`,
      path: `/pages/customer/detail/index?id=${this.data.customerId}`
    }
  }
})
