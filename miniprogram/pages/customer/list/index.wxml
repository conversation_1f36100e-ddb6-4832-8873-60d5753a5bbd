<view class="container">
  <!-- 搜索框 -->
  <view class="ios-search">
    <text class="ios-search-icon">🔍</text>
    <input class="ios-search-input" placeholder="搜索客户" bindinput="onSearchInput" value="{{keyword}}" confirm-type="search" bindconfirm="onSearch" />
  </view>

  <!-- 客户列表 -->
  <view class="ios-list">
    <block wx:if="{{customers.length > 0}}">
      <view class="ios-list-item" wx:for="{{customers}}" wx:key="_id" bindtap="goToCustomerDetail" data-id="{{item._id}}">
        <view class="customer-info">
          <view class="customer-name">{{item.name}}</view>
          <view class="customer-meta">
            <text class="customer-phone">{{item.phone}}</text>
            <text class="customer-join-date">加入时间: {{item.joinDate}}</text>
          </view>
          <view class="customer-tags">
            <text class="ios-tag ios-tag-blue" wx:if="{{item.isNewCustomer}}">新客户</text>
            <text class="ios-tag ios-tag-green" wx:if="{{item.memberExpireDate && item.memberExpireDate > now}}">会员有效</text>
            <text class="ios-tag ios-tag-orange" wx:if="{{item.memberExpireDate && item.memberExpireDate <= now}}">会员过期</text>
            <text class="ios-tag ios-tag-red" wx:if="{{!item.lastTraining}}">未训练</text>
          </view>
        </view>
        <view class="customer-arrow">
          <text>></text>
        </view>
      </view>
    </block>
    <view class="empty-list" wx:else>
      <text>暂无客户数据</text>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <text bindtap="loadMore">加载更多</text>
  </view>

  <!-- 添加客户按钮 -->
  <view class="ios-fab" bindtap="goToAddCustomer">
    <text>+</text>
  </view>
</view>
