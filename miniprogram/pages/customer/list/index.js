// 客户列表页
const app = getApp()

Page({
  data: {
    customers: [],
    keyword: '',
    page: 1,
    pageSize: 10,
    total: 0,
    hasMore: false,
    loading: false,
    now: new Date().getTime()
  },

  onLoad: function (options) {
    this.loadCustomers()
  },

  onPullDownRefresh: function () {
    this.setData({
      customers: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadCustomers(() => {
        wx.stopPullDownRefresh()
      })
    })
  },

  // 加载客户列表
  loadCustomers: function (callback) {
    const { keyword, page, pageSize } = this.data
    
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    wx.cloud.callFunction({
      name: 'customerService',
      data: {
        action: 'getCustomerList',
        data: {
          keyword,
          page,
          pageSize
        }
      }
    }).then(res => {
      const { success, data, total } = res.result
      
      if (success) {
        // 处理日期格式
        const customers = data.map(customer => {
          // 格式化加入日期
          if (customer.joinDate) {
            const date = new Date(customer.joinDate)
            customer.joinDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
          }
          
          // 判断是否为新客户（3个月内）
          if (customer.joinDate) {
            const joinDate = new Date(customer.joinDate).getTime()
            const threeMonthsAgo = new Date()
            threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)
            customer.isNewCustomer = joinDate > threeMonthsAgo.getTime()
          }
          
          return customer
        })
        
        this.setData({
          customers: page === 1 ? customers : [...this.data.customers, ...customers],
          total,
          hasMore: this.data.customers.length + customers.length < total
        })
      } else {
        wx.showToast({
          title: '获取客户列表失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取客户列表失败', err)
      wx.showToast({
        title: '获取客户列表失败',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({ loading: false })
      callback && callback()
    })
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      keyword: e.detail.value
    })
  },

  // 搜索确认
  onSearch: function () {
    this.setData({
      customers: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadCustomers()
    })
  },

  // 加载更多
  loadMore: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      }, () => {
        this.loadCustomers()
      })
    }
  },

  // 跳转到客户详情页
  goToCustomerDetail: function (e) {
    const customerId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/customer/detail/index?id=${customerId}`
    })
  },

  // 跳转到添加客户页
  goToAddCustomer: function () {
    wx.navigateTo({
      url: '/pages/customer/add/index'
    })
  },

  onShareAppMessage: function () {
    return {
      title: '客户管理系统',
      path: '/pages/customer/list/index'
    }
  }
})
