<view class="container">
  <form bindsubmit="submitForm">
    <!-- 基本信息 -->
    <view class="form-section-title">基本信息</view>
    <view class="ios-form-group">
      <view class="ios-form-item">
        <text class="ios-form-label">会员卡名称</text>
        <input class="ios-form-input" name="name" placeholder="请输入会员卡名称" value="{{card.name}}" />
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">会员卡类型</text>
        <picker bindchange="bindTypeChange" value="{{typeIndex}}" range="{{cardTypes}}" range-key="label">
          <view class="ios-form-input picker">
            <text>{{cardTypes[typeIndex].label}}</text>
          </view>
        </picker>
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">价格</text>
        <input class="ios-form-input" name="price" type="digit" placeholder="请输入价格" value="{{card.price}}" />
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">有效期(月)</text>
        <input class="ios-form-input" name="duration" type="number" placeholder="请输入有效期(月)" value="{{card.duration}}" />
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">训练次数</text>
        <input class="ios-form-input" name="trainingCount" type="number" placeholder="请输入包含的训练次数" value="{{card.trainingCount}}" />
      </view>
    </view>

    <!-- 包含产品 -->
    <view class="form-section-title">包含产品</view>
    <view class="ios-form-group">
      <view class="ios-form-item" bindtap="showProductSelector">
        <text class="ios-form-label">添加产品</text>
        <view class="ios-form-input add-product">
          <text>点击添加产品</text>
          <text class="add-icon">+</text>
        </view>
      </view>

      <!-- 已选产品列表 -->
      <view class="selected-products" wx:if="{{selectedProducts.length > 0}}">
        <view class="selected-product" wx:for="{{selectedProducts}}" wx:key="productId">
          <view class="product-info">
            <text class="product-name">{{item.name}}</text>
            <text class="product-price">¥{{item.price}}</text>
          </view>
          <view class="product-count">
            <text class="count-label">数量：</text>
            <view class="count-control">
              <text class="count-btn" bindtap="decreaseCount" data-index="{{index}}">-</text>
              <text class="count-value">{{item.count}}</text>
              <text class="count-btn" bindtap="increaseCount" data-index="{{index}}">+</text>
            </view>
          </view>
          <view class="product-remove" bindtap="removeProduct" data-index="{{index}}">
            <text class="remove-icon">×</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义权益 -->
    <view class="form-section-title">自定义权益</view>
    <view class="ios-form-group">
      <view class="ios-form-item" bindtap="showBenefitEditor">
        <text class="ios-form-label">添加权益</text>
        <view class="ios-form-input add-product">
          <text>点击添加权益</text>
          <text class="add-icon">+</text>
        </view>
      </view>

      <!-- 已添加权益列表 -->
      <view class="selected-benefits" wx:if="{{benefits.length > 0}}">
        <view class="selected-benefit" wx:for="{{benefits}}" wx:key="index">
          <view class="benefit-info">
            <text class="benefit-name">{{item.name}}</text>
            <text class="benefit-count">数量: {{item.count}}</text>
          </view>
          <view class="benefit-description" wx:if="{{item.description}}">
            {{item.description}}
          </view>
          <view class="benefit-actions">
            <text class="benefit-edit" bindtap="editBenefit" data-index="{{index}}">编辑</text>
            <text class="benefit-remove" bindtap="removeBenefit" data-index="{{index}}">删除</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 其他信息 -->
    <view class="form-section-title">其他信息</view>
    <view class="ios-form-group">
      <view class="ios-form-item notes-item">
        <text class="ios-form-label">描述</text>
        <textarea class="ios-form-textarea" name="description" placeholder="请输入会员卡描述" value="{{card.description}}" />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="form-actions">
      <button class="ios-btn-secondary" bindtap="cancelForm">取消</button>
      <button class="ios-btn" form-type="submit">保存</button>
    </view>
  </form>

  <!-- 产品选择器弹窗 -->
  <view class="product-selector-mask" wx:if="{{showSelector}}" bindtap="hideProductSelector">
    <view class="product-selector" catchtap="preventBubble">
      <view class="selector-header">
        <text class="selector-title">选择产品</text>
        <text class="selector-close" bindtap="hideProductSelector">×</text>
      </view>

      <view class="selector-search">
        <input class="selector-search-input" placeholder="搜索产品" bindinput="onProductSearchInput" confirm-type="search" bindconfirm="searchProducts" />
      </view>

      <scroll-view class="selector-content" scroll-y="true">
        <view class="selector-empty" wx:if="{{products.length === 0}}">
          <text>暂无产品数据</text>
        </view>
        <view class="selector-item" wx:for="{{products}}" wx:key="_id" bindtap="selectProduct" data-product="{{item}}">
          <view class="selector-item-info">
            <text class="selector-item-name">{{item.name}}</text>
            <text class="selector-item-price">¥{{item.price}}</text>
          </view>
          <view class="selector-item-category">
            <text class="ios-tag ios-tag-blue">{{item.category}}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 权益编辑弹窗 -->
  <view class="benefit-editor-mask" wx:if="{{showBenefitEditor}}" bindtap="hideBenefitEditor">
    <view class="benefit-editor" catchtap="preventBubble">
      <view class="editor-header">
        <text class="editor-title">{{editingBenefitIndex >= 0 ? '编辑权益' : '添加权益'}}</text>
        <text class="editor-close" bindtap="hideBenefitEditor">×</text>
      </view>

      <view class="editor-form">
        <view class="editor-form-item">
          <text class="editor-form-label">权益名称</text>
          <input class="editor-form-input" placeholder="请输入权益名称" value="{{editingBenefit.name}}" bindinput="onBenefitNameInput" />
        </view>
        <view class="editor-form-item">
          <text class="editor-form-label">数量</text>
          <input class="editor-form-input" type="number" placeholder="请输入数量" value="{{editingBenefit.count}}" bindinput="onBenefitCountInput" />
        </view>
        <view class="editor-form-item">
          <text class="editor-form-label">描述</text>
          <textarea class="editor-form-textarea" placeholder="请输入权益描述" value="{{editingBenefit.description}}" bindinput="onBenefitDescriptionInput" />
        </view>
      </view>

      <view class="editor-actions">
        <button class="ios-btn-secondary" bindtap="hideBenefitEditor">取消</button>
        <button class="ios-btn" bindtap="saveBenefit">保存</button>
      </view>
    </view>
  </view>
</view>
