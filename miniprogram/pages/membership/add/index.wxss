/* 添加会员卡页样式 */
.container {
  padding: 16px;
}

.form-section-title {
  font-size: 14px;
  color: #8E8E93;
  margin: 20px 0 10px 10px;
}

.ios-form-textarea {
  width: 100%;
  height: 100px;
  font-size: 16px;
  padding: 10px 0;
}

.notes-item {
  height: auto;
  min-height: 100px;
}

.add-product {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #007AFF;
}

.add-icon {
  font-size: 20px;
  font-weight: bold;
}

/* 已选产品列表 */
.selected-products {
  padding: 0 16px;
}

.selected-product {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 0.5px solid #E5E5EA;
}

.selected-product:last-child {
  border-bottom: none;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  margin-bottom: 4px;
  display: block;
}

.product-price {
  font-size: 12px;
  color: #FF3B30;
}

.product-count {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.count-label {
  font-size: 12px;
  color: #8E8E93;
  margin-right: 5px;
}

.count-control {
  display: flex;
  align-items: center;
}

.count-btn {
  width: 24px;
  height: 24px;
  background-color: #F2F2F7;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #007AFF;
}

.count-value {
  margin: 0 8px;
  font-size: 14px;
  min-width: 20px;
  text-align: center;
}

.product-remove {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.remove-icon {
  font-size: 20px;
  color: #FF3B30;
}

/* 产品选择器弹窗 */
.product-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-selector {
  width: 80%;
  max-height: 70%;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 0.5px solid #E5E5EA;
}

.selector-title {
  font-size: 16px;
  font-weight: 600;
}

.selector-close {
  font-size: 20px;
  color: #8E8E93;
}

.selector-search {
  padding: 10px 16px;
  border-bottom: 0.5px solid #E5E5EA;
}

.selector-search-input {
  background-color: #F2F2F7;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
}

.selector-content {
  flex: 1;
  max-height: 400px;
}

.selector-empty {
  padding: 30px 0;
  text-align: center;
  color: #8E8E93;
}

.selector-item {
  padding: 12px 16px;
  border-bottom: 0.5px solid #E5E5EA;
}

.selector-item-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.selector-item-name {
  font-size: 14px;
}

.selector-item-price {
  font-size: 14px;
  color: #FF3B30;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  margin-bottom: 30px;
}

.form-actions .ios-btn,
.form-actions .ios-btn-secondary {
  width: 48%;
}
