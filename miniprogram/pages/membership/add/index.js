// 添加/编辑会员卡页
const app = getApp()

Page({
  data: {
    cardId: '',
    card: {},
    isEdit: false,
    showSelector: false,
    products: [],
    selectedProducts: [],
    productKeyword: '',
    // 会员卡类型选项
    cardTypes: [
      { value: 'basic', label: '基础卡' },
      { value: 'premium', label: '高级卡' },
      { value: 'annual', label: '年度卡' },
      { value: 'custom', label: '定制卡' }
    ],
    typeIndex: 0,
    // 自定义权益相关
    benefits: [],
    showBenefitEditor: false,
    editingBenefit: {
      name: '',
      count: 1,
      description: ''
    },
    editingBenefitIndex: -1
  },

  onLoad: function (options) {
    if (options.id) {
      // 编辑模式
      this.setData({
        cardId: options.id,
        isEdit: true
      })
      wx.setNavigationBarTitle({
        title: '编辑会员卡'
      })
      this.loadCardDetail()
    }
  },

  // 加载会员卡详情
  loadCardDetail: function () {
    const { cardId, cardTypes } = this.data

    wx.showLoading({
      title: '加载中...',
    })

    wx.cloud.callFunction({
      name: 'membershipService',
      data: {
        action: 'getCardDetail',
        data: {
          cardId
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success && data) {
        // 处理包含的产品
        let selectedProducts = []
        if (data.products && data.products.length > 0 && data.productDetails) {
          selectedProducts = data.products.map(product => {
            const productDetail = data.productDetails.find(p => p._id === product.productId)
            return {
              productId: product.productId,
              name: productDetail ? productDetail.name : '未知产品',
              price: productDetail ? productDetail.price : 0,
              count: product.count
            }
          })
        }

        // 处理会员卡类型
        let typeIndex = 0
        if (data.type) {
          const index = cardTypes.findIndex(item => item.value === data.type)
          if (index >= 0) {
            typeIndex = index
          }
        }

        // 处理自定义权益
        let benefits = []
        if (data.benefits && data.benefits.length > 0) {
          benefits = [...data.benefits]
        }

        this.setData({
          card: data,
          selectedProducts,
          typeIndex,
          benefits
        })
      } else {
        wx.showToast({
          title: '获取会员卡详情失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取会员卡详情失败', err)
      wx.showToast({
        title: '获取会员卡详情失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 会员卡类型选择
  bindTypeChange: function (e) {
    this.setData({
      typeIndex: e.detail.value
    })
  },

  // 显示产品选择器
  showProductSelector: function () {
    this.setData({
      showSelector: true
    })
    this.loadProducts()
  },

  // 隐藏产品选择器
  hideProductSelector: function () {
    this.setData({
      showSelector: false
    })
  },

  // 防止冒泡
  preventBubble: function () {
    return
  },

  // 加载产品列表
  loadProducts: function () {
    const { productKeyword } = this.data

    wx.showLoading({
      title: '加载中...',
    })

    wx.cloud.callFunction({
      name: 'productService',
      data: {
        action: 'getProductList',
        data: {
          keyword: productKeyword,
          page: 1,
          pageSize: 100
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success) {
        this.setData({
          products: data
        })
      } else {
        wx.showToast({
          title: '获取产品列表失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取产品列表失败', err)
      wx.showToast({
        title: '获取产品列表失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 产品搜索输入
  onProductSearchInput: function (e) {
    this.setData({
      productKeyword: e.detail.value
    })
  },

  // 搜索产品
  searchProducts: function () {
    this.loadProducts()
  },

  // 选择产品
  selectProduct: function (e) {
    const product = e.currentTarget.dataset.product
    const { selectedProducts } = this.data

    // 检查是否已选择该产品
    const index = selectedProducts.findIndex(item => item.productId === product._id)

    if (index >= 0) {
      // 已选择，增加数量
      const newSelectedProducts = [...selectedProducts]
      newSelectedProducts[index].count += 1

      this.setData({
        selectedProducts: newSelectedProducts
      })
    } else {
      // 未选择，添加到已选列表
      this.setData({
        selectedProducts: [...selectedProducts, {
          productId: product._id,
          name: product.name,
          price: product.price,
          count: 1
        }]
      })
    }

    this.hideProductSelector()
  },

  // 增加产品数量
  increaseCount: function (e) {
    const index = e.currentTarget.dataset.index
    const { selectedProducts } = this.data

    const newSelectedProducts = [...selectedProducts]
    newSelectedProducts[index].count += 1

    this.setData({
      selectedProducts: newSelectedProducts
    })
  },

  // 减少产品数量
  decreaseCount: function (e) {
    const index = e.currentTarget.dataset.index
    const { selectedProducts } = this.data

    const newSelectedProducts = [...selectedProducts]
    if (newSelectedProducts[index].count > 1) {
      newSelectedProducts[index].count -= 1

      this.setData({
        selectedProducts: newSelectedProducts
      })
    }
  },

  // 移除产品
  removeProduct: function (e) {
    const index = e.currentTarget.dataset.index
    const { selectedProducts } = this.data

    const newSelectedProducts = [...selectedProducts]
    newSelectedProducts.splice(index, 1)

    this.setData({
      selectedProducts: newSelectedProducts
    })
  },

  // 显示权益编辑器
  showBenefitEditor: function () {
    this.setData({
      showBenefitEditor: true,
      editingBenefit: {
        name: '',
        count: 1,
        description: ''
      },
      editingBenefitIndex: -1
    })
  },

  // 隐藏权益编辑器
  hideBenefitEditor: function () {
    this.setData({
      showBenefitEditor: false
    })
  },

  // 权益名称输入
  onBenefitNameInput: function (e) {
    this.setData({
      'editingBenefit.name': e.detail.value
    })
  },

  // 权益数量输入
  onBenefitCountInput: function (e) {
    this.setData({
      'editingBenefit.count': parseInt(e.detail.value) || 1
    })
  },

  // 权益描述输入
  onBenefitDescriptionInput: function (e) {
    this.setData({
      'editingBenefit.description': e.detail.value
    })
  },

  // 编辑权益
  editBenefit: function (e) {
    const index = e.currentTarget.dataset.index
    const { benefits } = this.data

    this.setData({
      showBenefitEditor: true,
      editingBenefit: { ...benefits[index] },
      editingBenefitIndex: index
    })
  },

  // 移除权益
  removeBenefit: function (e) {
    const index = e.currentTarget.dataset.index
    const { benefits } = this.data

    const newBenefits = [...benefits]
    newBenefits.splice(index, 1)

    this.setData({
      benefits: newBenefits
    })
  },

  // 保存权益
  saveBenefit: function () {
    const { editingBenefit, editingBenefitIndex, benefits } = this.data

    // 表单验证
    if (!editingBenefit.name) {
      wx.showToast({
        title: '请输入权益名称',
        icon: 'none'
      })
      return
    }

    if (!editingBenefit.count || editingBenefit.count < 1) {
      wx.showToast({
        title: '请输入有效的数量',
        icon: 'none'
      })
      return
    }

    const newBenefits = [...benefits]

    if (editingBenefitIndex >= 0) {
      // 编辑现有权益
      newBenefits[editingBenefitIndex] = { ...editingBenefit }
    } else {
      // 添加新权益
      newBenefits.push({ ...editingBenefit })
    }

    this.setData({
      benefits: newBenefits,
      showBenefitEditor: false
    })
  },

  // 提交表单
  submitForm: function (e) {
    const formData = e.detail.value
    const { cardId, isEdit, selectedProducts } = this.data

    // 表单验证
    if (!formData.name) {
      wx.showToast({
        title: '请输入会员卡名称',
        icon: 'none'
      })
      return
    }

    if (!formData.price) {
      wx.showToast({
        title: '请输入价格',
        icon: 'none'
      })
      return
    }

    if (!formData.duration) {
      wx.showToast({
        title: '请输入有效期',
        icon: 'none'
      })
      return
    }

    if (!formData.trainingCount) {
      wx.showToast({
        title: '请输入训练次数',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: isEdit ? '更新中...' : '添加中...',
    })

    // 构建数据
    const data = {
      name: formData.name,
      price: parseFloat(formData.price),
      duration: parseInt(formData.duration),
      trainingCount: parseInt(formData.trainingCount),
      products: selectedProducts.map(item => ({
        productId: item.productId,
        count: item.count
      })),
      description: formData.description || ''
    }

    if (isEdit) {
      // 更新会员卡
      data._id = cardId

      wx.cloud.callFunction({
        name: 'membershipService',
        data: {
          action: 'updateCard',
          data
        }
      }).then(res => {
        const { success, message } = res.result

        if (success) {
          wx.showToast({
            title: '更新成功',
          })
          setTimeout(() => {
            this.safeNavigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: message || '更新失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        console.error('更新会员卡失败', err)
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        })
      }).finally(() => {
        wx.hideLoading()
      })
    } else {
      // 添加会员卡
      wx.cloud.callFunction({
        name: 'membershipService',
        data: {
          action: 'addCard',
          data
        }
      }).then(res => {
        const { success, message } = res.result

        if (success) {
          wx.showToast({
            title: '添加成功',
          })
          setTimeout(() => {
            this.safeNavigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: message || '添加失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        console.error('添加会员卡失败', err)
        wx.showToast({
          title: '添加失败',
          icon: 'none'
        })
      }).finally(() => {
        wx.hideLoading()
      })
    }
  },

  // 取消表单
  cancelForm: function () {
    this.safeNavigateBack()
  },

  // 安全的返回导航
  safeNavigateBack: function() {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      wx.navigateBack()
    } else {
      // 如果只有一个页面，跳转到会员卡列表页
      wx.redirectTo({
        url: '/pages/membership/list/index'
      })
    }
  }
})
