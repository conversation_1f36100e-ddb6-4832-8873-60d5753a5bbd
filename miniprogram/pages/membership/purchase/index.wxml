<view class="container">
  <!-- 客户信息 -->
  <view class="form-section-title">客户信息</view>
  <view class="ios-card customer-info">
    <view class="customer-name">{{customer.name}}</view>
    <view class="customer-phone">{{customer.phone}}</view>
    <view class="customer-membership" wx:if="{{membershipInfo}}">
      <text class="membership-label">当前会员状态：</text>
      <text class="membership-status {{membershipInfo.isValid ? 'valid' : 'invalid'}}">
        {{membershipInfo.isValid ? '有效' : '已过期'}}
      </text>
      <text class="membership-expire" wx:if="{{membershipInfo.isValid}}">
        剩余{{membershipInfo.remainingDays}}天
      </text>
    </view>
  </view>
  
  <!-- 会员卡选择 -->
  <view class="form-section-title">选择会员卡</view>
  <view class="ios-list membership-list">
    <block wx:if="{{cards.length > 0}}">
      <view class="ios-list-item membership-item {{selectedCardId === item._id ? 'selected' : ''}}" 
            wx:for="{{cards}}" 
            wx:key="_id"
            bindtap="selectCard"
            data-id="{{item._id}}">
        <view class="membership-info">
          <view class="membership-name">{{item.name}}</view>
          <view class="membership-meta">
            <text class="membership-duration">{{item.duration}}个月</text>
            <text class="membership-training">{{item.trainingCount}}次训练</text>
          </view>
          <view class="membership-description" wx:if="{{item.description}}">
            {{item.description}}
          </view>
        </view>
        <view class="membership-price">¥{{item.price}}</view>
      </view>
    </block>
    <view class="empty-list" wx:else>
      <text>暂无会员卡数据</text>
    </view>
  </view>
  
  <!-- 支付信息 -->
  <view class="form-section-title">支付信息</view>
  <view class="ios-form-group">
    <view class="ios-form-item">
      <text class="ios-form-label">支付金额</text>
      <input class="ios-form-input" type="digit" placeholder="请输入支付金额" value="{{paymentAmount}}" bindinput="onPaymentInput" />
    </view>
    <view class="ios-form-item">
      <text class="ios-form-label">操作类型</text>
      <picker bindchange="bindOperationChange" value="{{operationIndex}}" range="{{operations}}" range-key="name">
        <view class="ios-form-input {{operationIndex >= 0 ? '' : 'placeholder'}}">
          {{operationIndex >= 0 ? operations[operationIndex].name : '请选择操作类型'}}
        </view>
      </picker>
    </view>
  </view>
  
  <!-- 提交按钮 -->
  <view class="form-actions">
    <button class="ios-btn-secondary" bindtap="cancelForm">取消</button>
    <button class="ios-btn" bindtap="submitForm">确认</button>
  </view>
</view>
