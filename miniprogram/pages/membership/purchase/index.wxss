/* 购买会员卡页样式 */
.container {
  padding: 16px;
}

.form-section-title {
  font-size: 14px;
  color: #8E8E93;
  margin: 20px 0 10px 10px;
}

/* 客户信息 */
.customer-info {
  padding: 16px;
}

.customer-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.customer-phone {
  font-size: 14px;
  color: #8E8E93;
  margin-bottom: 10px;
}

.customer-membership {
  font-size: 14px;
  display: flex;
  align-items: center;
}

.membership-label {
  color: #8E8E93;
  margin-right: 5px;
}

.membership-status {
  font-weight: 500;
  margin-right: 10px;
}

.membership-status.valid {
  color: #34C759;
}

.membership-status.invalid {
  color: #FF3B30;
}

.membership-expire {
  color: #8E8E93;
}

/* 会员卡列表 */
.membership-list {
  margin-bottom: 20px;
}

.membership-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 0.5px solid #E5E5EA;
}

.membership-item:last-child {
  border-bottom: none;
}

.membership-item.selected {
  background-color: #E5F2FF;
}

.membership-info {
  flex: 1;
}

.membership-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #007AFF;
}

.membership-meta {
  font-size: 14px;
  color: #8E8E93;
  margin-bottom: 5px;
}

.membership-duration {
  margin-right: 10px;
}

.membership-description {
  font-size: 12px;
  color: #8E8E93;
}

.membership-price {
  font-size: 16px;
  font-weight: 600;
  color: #FF3B30;
}

.empty-list {
  padding: 30px 0;
  text-align: center;
  color: #8E8E93;
}

.placeholder {
  color: #C7C7CC;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  margin-bottom: 30px;
}

.form-actions .ios-btn,
.form-actions .ios-btn-secondary {
  width: 48%;
}
