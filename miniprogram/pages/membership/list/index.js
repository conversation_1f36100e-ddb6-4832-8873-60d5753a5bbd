// 会员卡列表页
const app = getApp()

Page({
  data: {
    cards: [],
    keyword: '',
    page: 1,
    pageSize: 10,
    total: 0,
    hasMore: false,
    loading: false
  },

  onLoad: function (options) {
    this.loadCards()
  },

  onPullDownRefresh: function () {
    this.setData({
      cards: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadCards(() => {
        wx.stopPullDownRefresh()
      })
    })
  },

  // 加载会员卡列表
  loadCards: function (callback) {
    const { keyword, page, pageSize } = this.data

    if (this.data.loading) return

    this.setData({ loading: true })

    wx.cloud.callFunction({
      name: 'productService',
      data: {
        action: 'getProductList',
        data: {
          keyword,
          type: 'membership', // 筛选会员卡类型
          page,
          pageSize
        }
      }
    }).then(res => {
      const { success, data, total } = res.result

      if (success) {
        this.setData({
          cards: page === 1 ? data : [...this.data.cards, ...data],
          total,
          hasMore: this.data.cards.length + data.length < total
        })
      } else {
        wx.showToast({
          title: '获取会员卡列表失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取会员卡列表失败', err)
      wx.showToast({
        title: '获取会员卡列表失败',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({ loading: false })
      callback && callback()
    })
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      keyword: e.detail.value
    })
  },

  // 搜索确认
  onSearch: function () {
    this.setData({
      cards: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadCards()
    })
  },

  // 加载更多
  loadMore: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      }, () => {
        this.loadCards()
      })
    }
  },

  // 编辑会员卡
  editCard: function (e) {
    const cardId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product/add/index?id=${cardId}&type=membership`
    })
  },

  // 删除会员卡
  deleteCard: function (e) {
    const cardId = e.currentTarget.dataset.id

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个会员卡吗？如果有客户正在使用该会员卡，将无法删除。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          })

          wx.cloud.callFunction({
            name: 'productService',
            data: {
              action: 'deleteProduct',
              data: {
                productId: cardId
              }
            }
          }).then(res => {
            const { success, message } = res.result

            if (success) {
              wx.showToast({
                title: '删除成功',
              })

              // 刷新列表
              this.setData({
                cards: this.data.cards.filter(item => item._id !== cardId)
              })
            } else {
              wx.showToast({
                title: message || '删除失败',
                icon: 'none'
              })
            }
          }).catch(err => {
            console.error('删除会员卡失败', err)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }).finally(() => {
            wx.hideLoading()
          })
        }
      }
    })
  },

  // 跳转到添加会员卡页
  goToAddCard: function () {
    wx.navigateTo({
      url: '/pages/product/add/index?type=membership'
    })
  },

  onShareAppMessage: function () {
    return {
      title: '会员卡管理',
      path: '/pages/membership/list/index'
    }
  }
})
