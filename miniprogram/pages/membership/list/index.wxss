/* 会员卡列表页样式 */
.container {
  padding: 16px;
}

/* 会员卡样式 */
.membership-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-name {
  font-size: 18px;
  font-weight: 600;
  color: #007AFF;
}

.card-price {
  font-size: 18px;
  font-weight: 600;
  color: #FF3B30;
}

.card-content {
  margin-bottom: 15px;
}

.card-info {
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 5px;
}

.info-label {
  width: 80px;
  color: #8E8E93;
  font-size: 14px;
}

.info-value {
  flex: 1;
  font-size: 14px;
}

/* 会员特权样式 */
.card-privileges {
  margin-top: 10px;
  margin-bottom: 10px;
}

.privileges-title {
  color: #8E8E93;
  font-size: 14px;
  margin-bottom: 5px;
  display: block;
}

.privileges-list {
  display: flex;
  flex-wrap: wrap;
}

.privilege-item {
  background-color: #E5F2FF;
  border-radius: 4px;
  padding: 4px 8px;
  margin-right: 8px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.privilege-name {
  font-size: 12px;
  color: #007AFF;
  margin-right: 5px;
}

.privilege-value {
  font-size: 12px;
  color: #007AFF;
  font-weight: 500;
}

/* 包含产品样式 */
.card-products {
  margin-top: 10px;
}

.products-title {
  color: #8E8E93;
  font-size: 14px;
  margin-bottom: 5px;
  display: block;
}

.product-list {
  display: flex;
  flex-wrap: wrap;
}

.product-item {
  background-color: #F2F2F7;
  border-radius: 4px;
  padding: 4px 8px;
  margin-right: 8px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.product-name {
  font-size: 12px;
  color: #000;
  margin-right: 5px;
}

.product-count {
  font-size: 12px;
  color: #8E8E93;
}

.card-actions {
  display: flex;
  justify-content: space-between;
}

.edit-btn, .delete-btn {
  width: 48%;
  margin: 0;
}

.empty-list {
  padding: 30px 0;
  text-align: center;
  color: #8E8E93;
}

.load-more {
  text-align: center;
  padding: 15px 0;
  color: #007AFF;
  font-size: 14px;
}

.add-btn {
  position: fixed;
  right: 20px;
  bottom: 30px;
  width: 50px;
  height: 50px;
  background-color: #007AFF;
  color: #fff;
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}
