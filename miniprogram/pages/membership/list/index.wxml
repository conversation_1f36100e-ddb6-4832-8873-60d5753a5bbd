<view class="container">
  <!-- 搜索框 -->
  <view class="ios-search">
    <text class="ios-search-icon">🔍</text>
    <input class="ios-search-input" placeholder="搜索会员卡" bindinput="onSearchInput" value="{{keyword}}" confirm-type="search" bindconfirm="onSearch" />
  </view>

  <!-- 会员卡列表 -->
  <view class="membership-list">
    <block wx:if="{{cards.length > 0}}">
      <view class="ios-card membership-card" wx:for="{{cards}}" wx:key="_id">
        <view class="card-header">
          <view class="card-name">{{item.name}}</view>
          <view class="card-price">¥{{item.price}}</view>
        </view>

        <view class="card-content">
          <view class="card-info">
            <view class="info-item">
              <text class="info-label">有效期：</text>
              <text class="info-value">{{item.membershipInfo.duration}}个月</text>
            </view>
            <view class="info-item">
              <text class="info-label">训练次数：</text>
              <text class="info-value">{{item.membershipInfo.trainingCount}}次</text>
            </view>
            <view class="info-item" wx:if="{{item.membershipInfo.level}}">
              <text class="info-label">会员等级：</text>
              <text class="info-value">{{item.membershipInfo.level}}</text>
            </view>
            <view class="info-item" wx:if="{{item.description}}">
              <text class="info-label">描述：</text>
              <text class="info-value">{{item.description}}</text>
            </view>
          </view>

          <!-- 会员特权 -->
          <view class="card-privileges" wx:if="{{item.membershipInfo.privileges}}">
            <text class="privileges-title">会员特权：</text>
            <view class="privileges-list">
              <view class="privilege-item" wx:if="{{item.membershipInfo.privileges.discount < 1}}">
                <text class="privilege-name">折扣：</text>
                <text class="privilege-value">{{item.membershipInfo.privileges.discount * 10}}折</text>
              </view>
              <view class="privilege-item" wx:if="{{item.membershipInfo.privileges.pointsMultiplier > 1}}">
                <text class="privilege-name">积分倍数：</text>
                <text class="privilege-value">{{item.membershipInfo.privileges.pointsMultiplier}}倍</text>
              </view>
              <view class="privilege-item" wx:if="{{item.membershipInfo.privileges.freeShipping}}">
                <text class="privilege-name">免运费</text>
              </view>
              <view class="privilege-item" wx:if="{{item.membershipInfo.privileges.priorityService}}">
                <text class="privilege-name">优先服务</text>
              </view>
            </view>
          </view>

          <!-- 包含产品 -->
          <view class="card-products" wx:if="{{item.membershipInfo.includedProducts && item.membershipInfo.includedProducts.length > 0}}">
            <text class="products-title">包含产品：</text>
            <view class="product-list">
              <view class="product-item" wx:for="{{item.membershipInfo.includedProducts}}" wx:for-item="product" wx:key="productId">
                <text class="product-name">{{product.name || '未知产品'}}</text>
                <text class="product-count">x{{product.count}}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="card-actions">
          <button class="edit-btn" bindtap="editCard" data-id="{{item._id}}">编辑</button>
          <button class="delete-btn" bindtap="deleteCard" data-id="{{item._id}}">删除</button>
        </view>
      </view>
    </block>
    <view class="empty-list" wx:else>
      <text>暂无会员卡数据</text>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <text bindtap="loadMore">加载更多</text>
  </view>

  <!-- 添加会员卡按钮 -->
  <view class="add-btn" bindtap="goToAddCard">
    <text>+</text>
  </view>
</view>
