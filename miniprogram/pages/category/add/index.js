// 添加/编辑分类页
const app = getApp()

Page({
  data: {
    categoryId: '',
    category: {},
    isEdit: false,
    // 分类类型选项
    typeOptions: [
      { value: 'normal', label: '普通产品' },
      { value: 'membership', label: '会员卡' }
    ],
    typeIndex: 0,
    // 会员卡等级选项（仅当类型为会员卡时显示）
    levelOptions: [
      { value: 'basic', label: '基础卡' },
      { value: 'premium', label: '高级卡' },
      { value: 'annual', label: '年度卡' },
      { value: 'custom', label: '定制卡' }
    ],
    levelIndex: 0,
    showLevelField: false
  },

  onLoad: function (options) {
    if (options.id) {
      // 编辑模式
      this.setData({
        categoryId: options.id,
        isEdit: true
      })
      wx.setNavigationBarTitle({
        title: '编辑分类'
      })
      this.loadCategoryDetail()
    }
  },

  // 加载分类详情
  loadCategoryDetail: function () {
    const { categoryId } = this.data

    wx.showLoading({
      title: '加载中...',
    })

    wx.cloud.callFunction({
      name: 'categoryService',
      data: {
        action: 'getCategoryDetail',
        data: {
          categoryId
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success && data) {
        // 设置类型索引
        const typeIndex = this.data.typeOptions.findIndex(item => item.value === data.type)

        // 设置等级索引（如果是会员卡类型）
        let levelIndex = 0
        if (data.type === 'membership' && data.level) {
          levelIndex = this.data.levelOptions.findIndex(item => item.value === data.level)
          if (levelIndex < 0) levelIndex = 0
        }

        this.setData({
          category: data,
          typeIndex: typeIndex >= 0 ? typeIndex : 0,
          levelIndex,
          showLevelField: data.type === 'membership'
        })
      } else {
        wx.showToast({
          title: '获取分类详情失败',
          icon: 'none'
        })
        setTimeout(() => {
          this.safeNavigateBack()
        }, 1500)
      }
    }).catch(err => {
      console.error('获取分类详情失败', err)
      wx.showToast({
        title: '获取分类详情失败',
        icon: 'none'
      })
      setTimeout(() => {
        this.safeNavigateBack()
      }, 1500)
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 类型选择器变化
  bindTypeChange: function (e) {
    const typeIndex = e.detail.value
    const showLevelField = this.data.typeOptions[typeIndex].value === 'membership'

    this.setData({
      typeIndex,
      showLevelField
    })
  },

  // 等级选择器变化
  bindLevelChange: function (e) {
    this.setData({
      levelIndex: e.detail.value
    })
  },

  // 提交表单
  submitForm: function (e) {
    const formData = e.detail.value
    const { categoryId, isEdit, typeOptions, typeIndex, levelOptions, levelIndex, showLevelField } = this.data

    // 表单验证
    if (!formData.name) {
      wx.showToast({
        title: '请输入分类名称',
        icon: 'none'
      })
      return
    }

    // 构建提交数据
    const type = typeOptions[typeIndex].value
    const data = {
      name: formData.name,
      type,
      description: formData.description || ''
    }

    // 如果是会员卡类型，添加等级字段
    if (type === 'membership') {
      data.level = levelOptions[levelIndex].value
    }

    // 如果是编辑模式，添加ID
    if (isEdit) {
      data._id = categoryId
    }

    wx.showLoading({
      title: isEdit ? '更新中...' : '添加中...',
    })

    // 调用云函数
    wx.cloud.callFunction({
      name: 'categoryService',
      data: {
        action: isEdit ? 'updateCategory' : 'addCategory',
        data
      }
    }).then(res => {
      const { success, message } = res.result

      if (success) {
        wx.showToast({
          title: isEdit ? '更新成功' : '添加成功',
        })
        setTimeout(() => {
          this.safeNavigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: message || (isEdit ? '更新失败' : '添加失败'),
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error(isEdit ? '更新分类失败' : '添加分类失败', err)
      wx.showToast({
        title: isEdit ? '更新失败' : '添加失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 取消表单
  cancelForm: function () {
    this.safeNavigateBack()
  },

  // 安全的返回导航
  safeNavigateBack: function() {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      wx.navigateBack()
    } else {
      // 如果只有一个页面，跳转到分类列表页
      wx.redirectTo({
        url: '/pages/category/list/index'
      })
    }
  }
})
