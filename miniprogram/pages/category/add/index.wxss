.container {
  padding: 20rpx;
}

.form-section-title {
  font-size: 28rpx;
  color: #8e8e93;
  margin: 30rpx 20rpx 10rpx;
}

.ios-form-group {
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.ios-form-item {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e5e5ea;
}

.ios-form-item:last-child {
  border-bottom: none;
}

.ios-form-label {
  display: block;
  font-size: 28rpx;
  color: #8e8e93;
  margin-bottom: 10rpx;
}

.ios-form-input {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  color: #000;
}

.ios-form-textarea {
  width: 100%;
  height: 200rpx;
  font-size: 32rpx;
  color: #000;
}

.picker {
  display: flex;
  align-items: center;
  height: 80rpx;
}

.ios-form-buttons {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
}

.ios-button {
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 10rpx;
  font-size: 32rpx;
}

.ios-button-primary {
  background-color: #007aff;
  color: #fff;
}

.ios-button-secondary {
  background-color: #f2f2f7;
  color: #007aff;
}
