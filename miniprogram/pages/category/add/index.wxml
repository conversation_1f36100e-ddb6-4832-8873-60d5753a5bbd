<view class="container">
  <form bindsubmit="submitForm">
    <!-- 基本信息 -->
    <view class="form-section-title">基本信息</view>
    <view class="ios-form-group">
      <view class="ios-form-item">
        <text class="ios-form-label">分类名称</text>
        <input class="ios-form-input" name="name" placeholder="请输入分类名称" value="{{category.name}}" />
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">分类类型</text>
        <picker bindchange="bindTypeChange" value="{{typeIndex}}" range="{{typeOptions}}" range-key="label">
          <view class="ios-form-input picker">
            <text>{{typeOptions[typeIndex].label}}</text>
          </view>
        </picker>
      </view>
      <view class="ios-form-item" wx:if="{{showLevelField}}">
        <text class="ios-form-label">会员卡等级</text>
        <picker bindchange="bindLevelChange" value="{{levelIndex}}" range="{{levelOptions}}" range-key="label">
          <view class="ios-form-input picker">
            <text>{{levelOptions[levelIndex].label}}</text>
          </view>
        </picker>
      </view>
      <view class="ios-form-item">
        <text class="ios-form-label">描述</text>
        <textarea class="ios-form-textarea" name="description" placeholder="请输入分类描述" value="{{category.description}}"></textarea>
      </view>
    </view>

    <!-- 按钮 -->
    <view class="ios-form-buttons">
      <button class="ios-button ios-button-secondary" bindtap="cancelForm">取消</button>
      <button class="ios-button ios-button-primary" form-type="submit">保存</button>
    </view>
  </form>
</view>
