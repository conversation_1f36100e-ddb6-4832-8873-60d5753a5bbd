<view class="container">
  <!-- 搜索栏 -->
  <view class="ios-search-bar">
    <view class="ios-search-input-wrapper">
      <icon type="search" size="14" color="#8e8e93"></icon>
      <input class="ios-search-input" placeholder="搜索分类" bindinput="onSearchInput" bindconfirm="onSearch" confirm-type="search" value="{{keyword}}" />
      <view class="ios-search-clear" wx:if="{{keyword}}" bindtap="clearSearch">
        <icon type="clear" size="14" color="#8e8e93"></icon>
      </view>
    </view>
  </view>
  
  <!-- 类型筛选 -->
  <view class="type-filter">
    <view class="type-item {{currentType === '' ? 'active' : ''}}" bindtap="filterByType" data-type="">
      全部
    </view>
    <view class="type-item {{currentType === 'normal' ? 'active' : ''}}" bindtap="filterByType" data-type="normal">
      普通产品
    </view>
    <view class="type-item {{currentType === 'membership' ? 'active' : ''}}" bindtap="filterByType" data-type="membership">
      会员卡
    </view>
  </view>
  
  <!-- 分类列表 -->
  <view class="category-list">
    <block wx:if="{{categories.length > 0}}">
      <view class="ios-card category-card" wx:for="{{categories}}" wx:key="_id">
        <view class="category-info">
          <view class="category-name">{{item.name}}</view>
          <view class="category-type">
            <text class="ios-tag {{item.type === 'normal' ? 'ios-tag-blue' : 'ios-tag-green'}}">
              {{item.type === 'normal' ? '普通产品' : '会员卡'}}
            </text>
          </view>
          <view class="category-description" wx:if="{{item.description}}">
            {{item.description}}
          </view>
        </view>
        <view class="category-actions">
          <button class="ios-button ios-button-small" bindtap="editCategory" data-id="{{item._id}}">编辑</button>
          <button class="ios-button ios-button-small ios-button-danger" bindtap="deleteCategory" data-id="{{item._id}}">删除</button>
        </view>
      </view>
    </block>
    <view class="empty-tip" wx:else>
      <text>暂无分类数据</text>
    </view>
  </view>
  
  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMore">
    <text>加载更多</text>
  </view>
  
  <!-- 添加按钮 -->
  <view class="ios-fab" bindtap="goToAddCategory">
    <text>+</text>
  </view>
</view>
