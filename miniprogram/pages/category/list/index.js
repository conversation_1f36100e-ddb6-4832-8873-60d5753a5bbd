// 分类列表页
const app = getApp()

Page({
  data: {
    categories: [],
    keyword: '',
    currentType: '', // 当前筛选类型：normal或membership
    page: 1,
    pageSize: 10,
    total: 0,
    hasMore: false,
    loading: false
  },

  onLoad: function (options) {
    this.loadCategories()
  },

  onPullDownRefresh: function () {
    this.setData({
      categories: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadCategories(() => {
        wx.stopPullDownRefresh()
      })
    })
  },

  // 加载分类列表
  loadCategories: function (callback) {
    const { keyword, currentType, page, pageSize } = this.data
    
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    wx.cloud.callFunction({
      name: 'categoryService',
      data: {
        action: 'getCategoryList',
        data: {
          keyword,
          type: currentType,
          page,
          pageSize
        }
      }
    }).then(res => {
      const { success, data, total } = res.result
      
      if (success) {
        this.setData({
          categories: page === 1 ? data : [...this.data.categories, ...data],
          total,
          hasMore: this.data.categories.length + data.length < total
        })
      } else {
        wx.showToast({
          title: '获取分类列表失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取分类列表失败', err)
      wx.showToast({
        title: '获取分类列表失败',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({ loading: false })
      callback && callback()
    })
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      keyword: e.detail.value
    })
  },

  // 搜索确认
  onSearch: function () {
    this.setData({
      categories: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadCategories()
    })
  },

  // 类型筛选
  filterByType: function (e) {
    const type = e.currentTarget.dataset.type
    
    this.setData({
      currentType: type,
      categories: [],
      page: 1,
      hasMore: false
    }, () => {
      this.loadCategories()
    })
  },

  // 加载更多
  loadMore: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      }, () => {
        this.loadCategories()
      })
    }
  },

  // 编辑分类
  editCategory: function (e) {
    const categoryId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/category/add/index?id=${categoryId}`
    })
  },

  // 删除分类
  deleteCategory: function (e) {
    const categoryId = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个分类吗？如果有产品使用该分类，将无法删除。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          })
          
          wx.cloud.callFunction({
            name: 'categoryService',
            data: {
              action: 'deleteCategory',
              data: {
                categoryId
              }
            }
          }).then(res => {
            const { success, message } = res.result
            
            if (success) {
              wx.showToast({
                title: '删除成功',
              })
              
              // 刷新列表
              this.setData({
                categories: this.data.categories.filter(item => item._id !== categoryId)
              })
            } else {
              wx.showToast({
                title: message || '删除失败',
                icon: 'none'
              })
            }
          }).catch(err => {
            console.error('删除分类失败', err)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }).finally(() => {
            wx.hideLoading()
          })
        }
      }
    })
  },

  // 跳转到添加分类页
  goToAddCategory: function () {
    wx.navigateTo({
      url: '/pages/category/add/index'
    })
  },

  onShareAppMessage: function () {
    return {
      title: '分类管理',
      path: '/pages/category/list/index'
    }
  }
})
