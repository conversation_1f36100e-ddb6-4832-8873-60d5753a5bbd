.container {
  padding: 20rpx;
  padding-bottom: 120rpx;
}

/* 搜索栏样式 */
.ios-search-bar {
  padding: 10rpx 20rpx;
  background-color: #f2f2f7;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.ios-search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #e5e5ea;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
}

.ios-search-input {
  flex: 1;
  height: 60rpx;
  margin-left: 10rpx;
  font-size: 28rpx;
}

/* 类型筛选样式 */
.type-filter {
  display: flex;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.type-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #333;
  border-bottom: 4rpx solid transparent;
}

.type-item.active {
  color: #007aff;
  border-bottom-color: #007aff;
}

/* 分类卡片样式 */
.category-list {
  margin-bottom: 20rpx;
}

.category-card {
  margin-bottom: 20rpx;
  padding: 30rpx;
  border-radius: 10rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.category-info {
  margin-bottom: 20rpx;
}

.category-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.category-type {
  margin-bottom: 10rpx;
}

.ios-tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
}

.ios-tag-blue {
  background-color: #e3f2fd;
  color: #2196f3;
}

.ios-tag-green {
  background-color: #e8f5e9;
  color: #4caf50;
}

.category-description {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

.category-actions {
  display: flex;
  justify-content: flex-end;
}

.ios-button {
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  background-color: #007aff;
  color: #fff;
  margin-left: 20rpx;
  border: none;
}

.ios-button-small {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  min-width: 100rpx;
}

.ios-button-danger {
  background-color: #ff3b30;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #007aff;
  font-size: 28rpx;
}

/* 悬浮添加按钮 */
.ios-fab {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #007aff;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 50rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}
