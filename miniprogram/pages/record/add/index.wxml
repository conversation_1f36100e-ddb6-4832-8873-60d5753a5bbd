<view class="container">
  <!-- 客户选择 -->
  <view class="form-section-title">客户信息</view>
  <view class="ios-form-group">
    <view class="ios-form-item">
      <text class="ios-form-label">客户</text>
      <picker bindchange="bindCustomerChange" value="{{customerIndex}}" range="{{customers}}" range-key="name" disabled="{{!!customerId}}">
        <view class="ios-form-input {{customerIndex >= 0 ? '' : 'placeholder'}}">
          {{customerIndex >= 0 ? customers[customerIndex].name : '请选择客户'}}
        </view>
      </picker>
    </view>
  </view>
  
  <!-- 记录类型选择 -->
  <view class="form-section-title">记录类型</view>
  <view class="ios-form-group">
    <view class="ios-form-item">
      <text class="ios-form-label">类型</text>
      <picker bindchange="bindTypeChange" value="{{typeIndex}}" range="{{recordTypes}}" range-key="name" disabled="{{!!recordType}}">
        <view class="ios-form-input {{typeIndex >= 0 ? '' : 'placeholder'}}">
          {{typeIndex >= 0 ? recordTypes[typeIndex].name : '请选择记录类型'}}
        </view>
      </picker>
    </view>
  </view>
  
  <!-- 训练记录表单 -->
  <block wx:if="{{currentType === 'training'}}">
    <form bindsubmit="submitTrainingForm">
      <view class="form-section-title">训练信息</view>
      <view class="ios-form-group">
        <view class="ios-form-item">
          <text class="ios-form-label">训练类型</text>
          <picker bindchange="bindTrainingTypeChange" value="{{trainingTypeIndex}}" range="{{trainingTypes}}" range-key="name">
            <view class="ios-form-input {{trainingTypeIndex >= 0 ? '' : 'placeholder'}}">
              {{trainingTypeIndex >= 0 ? trainingTypes[trainingTypeIndex].name : '请选择训练类型'}}
            </view>
          </picker>
        </view>
        <view class="ios-form-item">
          <text class="ios-form-label">训练时长</text>
          <input class="ios-form-input" name="duration" type="number" placeholder="请输入训练时长(分钟)" />
        </view>
        <view class="ios-form-item notes-item">
          <text class="ios-form-label">训练内容</text>
          <textarea class="ios-form-textarea" name="content" placeholder="请输入训练内容" />
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="form-actions">
        <button class="ios-btn-secondary" bindtap="cancelForm">取消</button>
        <button class="ios-btn" form-type="submit">保存</button>
      </view>
    </form>
  </block>
  
  <!-- 购买记录表单 -->
  <block wx:if="{{currentType === 'purchase'}}">
    <form bindsubmit="submitPurchaseForm">
      <view class="form-section-title">购买信息</view>
      <view class="ios-form-group">
        <view class="ios-form-item">
          <text class="ios-form-label">产品</text>
          <picker bindchange="bindProductChange" value="{{productIndex}}" range="{{products}}" range-key="name">
            <view class="ios-form-input {{productIndex >= 0 ? '' : 'placeholder'}}">
              {{productIndex >= 0 ? products[productIndex].name : '请选择产品'}}
            </view>
          </picker>
        </view>
        <view class="ios-form-item">
          <text class="ios-form-label">金额</text>
          <input class="ios-form-input" name="amount" type="digit" placeholder="请输入金额" value="{{productIndex >= 0 ? products[productIndex].price : ''}}" />
        </view>
        <view class="ios-form-item notes-item">
          <text class="ios-form-label">备注</text>
          <textarea class="ios-form-textarea" name="remark" placeholder="请输入备注信息" />
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="form-actions">
        <button class="ios-btn-secondary" bindtap="cancelForm">取消</button>
        <button class="ios-btn" form-type="submit">保存</button>
      </view>
    </form>
  </block>
</view>
