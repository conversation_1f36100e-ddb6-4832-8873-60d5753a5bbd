/* 记录列表页样式 */

.container {
  padding-bottom: 100px;
}

/* 分段控制器 */
.ios-segment-control {
  display: flex;
  background-color: #fff;
  border-radius: 10px;
  margin: 16px;
  padding: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.segment-item {
  flex: 1;
  text-align: center;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
}

.segment-item.active {
  background-color: #007AFF;
  color: #fff;
}

/* 记录信息 */
.record-info {
  padding: 16px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-title {
  font-size: 16px;
  font-weight: 500;
  color: #000;
}

.record-date {
  font-size: 12px;
  color: #999;
}

.record-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.customer-name {
  font-size: 14px;
  color: #666;
}

.record-duration {
  font-size: 12px;
  color: #007AFF;
  background-color: #E3F2FD;
  padding: 2px 6px;
  border-radius: 4px;
}

.record-price {
  font-size: 14px;
  color: #FF6B35;
  font-weight: 500;
}

.record-content {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  background-color: #F8F9FA;
  padding: 8px;
  border-radius: 6px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-text {
  font-size: 16px;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20px;
}

.load-more-text {
  font-size: 14px;
  color: #999;
}

/* 使用全局统一的 FAB 样式 */
