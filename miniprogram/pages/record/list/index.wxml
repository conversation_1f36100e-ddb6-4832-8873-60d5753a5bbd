<view class="container">


  <!-- 标签切换 -->
  <view class="ios-segment-control">
    <view class="segment-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
      训练记录
    </view>
    <view class="segment-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
      购买记录
    </view>
  </view>

  <!-- 训练记录列表 -->
  <view wx:if="{{currentTab === 0}}">
    <view class="ios-card" wx:if="{{trainingRecords.length > 0}}">
      <view class="ios-list">
        <view class="ios-list-item" wx:for="{{trainingRecords}}" wx:key="_id">
          <view class="record-info">
            <view class="record-header">
              <view class="record-title">{{item.typeName}}</view>
              <view class="record-date">{{item.date}}</view>
            </view>
            <view class="record-meta">
              <text class="customer-name">{{item.customer ? item.customer.name : '未知客户'}}</text>
              <text class="record-duration" wx:if="{{item.duration}}">{{item.duration}}分钟</text>
            </view>
            <view class="record-content" wx:if="{{item.content}}">
              {{item.content}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="empty-state" wx:else>
      <text class="empty-text">暂无训练记录</text>
    </view>
  </view>

  <!-- 购买记录列表 -->
  <view wx:if="{{currentTab === 1}}">
    <view class="ios-card" wx:if="{{purchaseRecords.length > 0}}">
      <view class="ios-list">
        <view class="ios-list-item" wx:for="{{purchaseRecords}}" wx:key="_id">
          <view class="record-info">
            <view class="record-header">
              <view class="record-title">{{item.product ? item.product.name : '未知产品'}}</view>
              <view class="record-date">{{item.date}}</view>
            </view>
            <view class="record-meta">
              <text class="customer-name">{{item.customer ? item.customer.name : '未知客户'}}</text>
              <text class="record-price">¥{{item.amount}}</text>
            </view>
            <view class="record-content" wx:if="{{item.remark}}">
              {{item.remark}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="empty-state" wx:else>
      <text class="empty-text">暂无购买记录</text>
    </view>
  </view>

  <!-- 加载更多提示 -->
  <view class="load-more" wx:if="{{loadingTraining || loadingPurchase}}">
    <text class="load-more-text">加载中...</text>
  </view>

  <!-- 添加记录按钮 -->
  <view class="fab-container">
    <view class="fab" bindtap="goToAddRecord">
      <text class="fab-icon">+</text>
    </view>
  </view>
</view>
