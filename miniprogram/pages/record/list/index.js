// 记录列表页
const app = getApp()

Page({
  data: {
    trainingRecords: [],
    purchaseRecords: [],
    currentTab: 0, // 0: 训练记录, 1: 购买记录
    trainingPage: 1,
    purchasePage: 1,
    pageSize: 10,
    hasMoreTraining: true,
    hasMorePurchase: true,
    loading: false
  },

  onLoad: function (options) {
    console.log('记录列表页面加载')
    this.loadTrainingRecords()
    this.loadPurchaseRecords()
  },

  onPullDownRefresh: function () {
    this.setData({
      trainingRecords: [],
      purchaseRecords: [],
      trainingPage: 1,
      purchasePage: 1,
      hasMoreTraining: true,
      hasMorePurchase: true
    })

    this.loadTrainingRecords()
    this.loadPurchaseRecords()

    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  onReachBottom: function () {
    if (this.data.currentTab === 0) {
      this.loadMoreTrainingRecords()
    } else {
      this.loadMorePurchaseRecords()
    }
  },

  // 切换标签
  switchTab: function (e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentTab: index
    })
  },

  // 加载训练记录
  loadTrainingRecords: function () {
    if (this.data.loading) return

    console.log('开始加载训练记录')
    this.setData({ loading: true })

    wx.cloud.callFunction({
      name: 'recordService',
      data: {
        action: 'getAllTrainingRecords',
        data: {
          page: this.data.trainingPage,
          pageSize: this.data.pageSize
        }
      }
    }).then(res => {
      console.log('训练记录云函数返回:', res)
      const { success, data } = res.result

      if (success) {
        console.log('训练记录数据:', data)
        const records = data.map(record => {
          // 格式化日期
          const date = new Date(record.createTime)
          record.date = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`

          // 格式化训练类型
          record.typeName = record.type === 'eye' ? '眼睛训练' : '视觉训练'

          return record
        })

        this.setData({
          trainingRecords: this.data.trainingPage === 1 ? records : [...this.data.trainingRecords, ...records],
          hasMoreTraining: records.length === this.data.pageSize
        })
      } else {
        console.error('获取训练记录失败:', res.result)
        wx.showToast({
          title: '获取训练记录失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取训练记录失败', err)
      wx.showToast({
        title: '获取训练记录失败',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({ loading: false })
    })
  },

  // 加载购买记录
  loadPurchaseRecords: function () {
    if (this.data.loading) return

    console.log('开始加载购买记录')
    this.setData({ loading: true })

    wx.cloud.callFunction({
      name: 'recordService',
      data: {
        action: 'getAllPurchaseRecords',
        data: {
          page: this.data.purchasePage,
          pageSize: this.data.pageSize
        }
      }
    }).then(res => {
      console.log('购买记录云函数返回:', res)
      const { success, data } = res.result

      if (success) {
        console.log('购买记录数据:', data)
        const records = data.map(record => {
          // 格式化日期
          const date = new Date(record.createTime)
          record.date = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`

          return record
        })

        this.setData({
          purchaseRecords: this.data.purchasePage === 1 ? records : [...this.data.purchaseRecords, ...records],
          hasMorePurchase: records.length === this.data.pageSize
        })
      } else {
        console.error('获取购买记录失败:', res.result)
        wx.showToast({
          title: '获取购买记录失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取购买记录失败', err)
      wx.showToast({
        title: '获取购买记录失败',
        icon: 'none'
      })
    }).finally(() => {
      this.setData({ loading: false })
    })
  },

  // 加载更多训练记录
  loadMoreTrainingRecords: function () {
    if (!this.data.hasMoreTraining || this.data.loading) return

    this.setData({
      trainingPage: this.data.trainingPage + 1
    })
    this.loadTrainingRecords()
  },

  // 加载更多购买记录
  loadMorePurchaseRecords: function () {
    if (!this.data.hasMorePurchase || this.data.loading) return

    this.setData({
      purchasePage: this.data.purchasePage + 1
    })
    this.loadPurchaseRecords()
  },

  // 跳转到添加记录页
  goToAddRecord: function () {
    wx.navigateTo({
      url: '/pages/record/add/index'
    })
  }
})
