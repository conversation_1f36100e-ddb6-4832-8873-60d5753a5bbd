/**app.wxss**/
/* iOS风格全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Arial, sans-serif;
  background-color: #f2f2f7;
  color: #000;
  font-size: 14px;
  line-height: 1.5;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 0 16px;
  width: 100%;
}

/* iOS风格按钮 */
.ios-btn {
  background-color: #007AFF;
  color: #fff;
  border-radius: 10px;
  font-weight: 500;
  padding: 12px 0;
  text-align: center;
  font-size: 16px;
  margin: 10px 0;
  border: none;
}

.ios-btn-secondary {
  background-color: #F2F2F7;
  color: #007AFF;
  border-radius: 10px;
  font-weight: 500;
  padding: 12px 0;
  text-align: center;
  font-size: 16px;
  margin: 10px 0;
  border: 1px solid #007AFF;
}

/* iOS风格卡片 */
.ios-card {
  background-color: #fff;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* iOS风格列表 */
.ios-list {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 20px;
}

.ios-list-item {
  padding: 16px;
  border-bottom: 0.5px solid #E5E5EA;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ios-list-item:last-child {
  border-bottom: none;
}

/* iOS风格表单 */
.ios-form-group {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 20px;
}

.ios-form-item {
  padding: 12px 16px;
  border-bottom: 0.5px solid #E5E5EA;
  display: flex;
  align-items: center;
}

.ios-form-item:last-child {
  border-bottom: none;
}

.ios-form-label {
  width: 100px;
  color: #000;
  font-size: 16px;
}

.ios-form-input {
  flex: 1;
  font-size: 16px;
}

/* iOS风格导航栏标题 */
.ios-nav-title {
  font-size: 17px;
  font-weight: 600;
  color: #000;
}

/* iOS风格标签 */
.ios-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 5px;
}

.ios-tag-blue {
  background-color: #E5F2FF;
  color: #007AFF;
}

.ios-tag-green {
  background-color: #E5F9E0;
  color: #34C759;
}

.ios-tag-orange {
  background-color: #FFF2E5;
  color: #FF9500;
}

.ios-tag-red {
  background-color: #FFE5E5;
  color: #FF3B30;
}

/* iOS风格分割线 */
.ios-divider {
  height: 0.5px;
  background-color: #E5E5EA;
  width: 100%;
  margin: 10px 0;
}

/* iOS风格头像 */
.ios-avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #E5E5EA;
  overflow: hidden;
}

/* iOS风格图标按钮 */
.ios-icon-btn {
  color: #007AFF;
  font-size: 22px;
  padding: 5px;
}

/* iOS风格搜索框 */
.ios-search {
  background-color: #E5E5EA;
  border-radius: 10px;
  padding: 8px 12px;
  margin: 10px 0;
  display: flex;
  align-items: center;
}

.ios-search-icon {
  margin-right: 5px;
  color: #8E8E93;
}

.ios-search-input {
  flex: 1;
  font-size: 16px;
  color: #000;
}

/* 清除微信小程序默认样式 */
button {
  background: initial;
  padding: 0;
  margin: 0;
  line-height: inherit;
}

button:focus{
  outline: 0;
}

button::after{
  border: none;
}