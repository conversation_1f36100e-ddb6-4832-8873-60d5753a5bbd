# 客户信息管理小程序

## 项目介绍

这是一个基于微信小程序云开发的客户信息管理系统，采用iOS风格设计，主要用于管理客户信息、训练记录和产品信息。

## 主要功能

1. **客户管理功能**
   - 客户列表页：展示所有客户信息，包括最近训练、会员有效期等
   - 客户详情页：展示客户详细信息、训练记录、购买记录等
   - 添加/编辑客户页：可以添加新客户或编辑现有客户信息

2. **记录管理功能**
   - 添加记录页：可以添加训练记录（眼睛训练、视觉训练）或购买记录

3. **产品管理功能**
   - 产品列表页：展示所有产品信息，可按分类筛选
   - 添加/编辑产品页：可以添加新产品或编辑现有产品信息
   - 分类管理页：可以添加、编辑和删除产品分类

## 技术架构

- 前端：微信小程序
- 后端：微信云开发
- 数据库：云数据库
- 云函数：Node.js

## 数据库设计

### 当前数据库设计

- **customers 集合**：存储客户信息
  - name: 姓名
  - phone: 电话
  - joinDate: 加入时间
  - membershipCardId: 会员卡ID
  - memberExpireDate: 会员有效期
  - remainingTrainingCount: 剩余训练次数
  - purchaseDate: 购买日期
  - notes: 备注
  - createTime: 创建时间

- **training_records 集合**：存储训练记录
  - customerId: 客户ID
  - type: 训练类型（eye/vision）
  - duration: 训练时长
  - content: 训练内容
  - createTime: 创建时间

- **products 集合**：存储产品信息
  - name: 产品名称
  - price: 价格
  - category: 分类
  - categoryId: 分类ID
  - description: 描述
  - createTime: 创建时间

- **purchase_records 集合**：存储购买记录
  - customerId: 客户ID
  - productId: 产品ID
  - amount: 金额
  - remark: 备注
  - createTime: 创建时间

- **membership_cards 集合**：存储会员卡类型信息
  - name: 会员卡名称
  - duration: 有效期（月）
  - price: 价格
  - trainingCount: 包含训练次数
  - products: 包含商品 [{ productId, count }]
  - description: 描述
  - createTime: 创建时间

- **categories 集合**：存储分类信息
  - name: 分类名称
  - type: 分类类型（normal/membership）
  - description: 描述
  - level: 会员卡等级（仅当type为membership时有效）
  - createTime: 创建时间
  - updateTime: 更新时间

### 新的数据库设计（会员卡作为特殊商品）

将会员卡作为特殊商品类型进行设计，统一管理产品和会员卡，同时保留会员卡的特殊属性。

- **products 集合**：存储产品信息（包含会员卡）
  - name: 产品名称
  - price: 价格
  - category: 分类
  - description: 描述
  - type: 产品类型 ('normal'普通产品 或 'membership'会员卡)
  - membershipInfo: 会员卡特有属性（当type为'membership'时有效）
    - duration: 有效期（月）
    - trainingCount: 包含训练次数
    - level: 会员等级（如'basic', 'premium', 'annual'）
    - privileges: 会员特权
      - discount: 会员折扣（如0.9表示9折）
      - pointsMultiplier: 积分倍数（如1.5表示1.5倍积分）
      - freeShipping: 是否免运费
      - priorityService: 是否优先服务
    - benefits: 会员权益 [{ name, count, description }]
    - includedProducts: 包含的产品 [{ productId, count }]
  - createTime: 创建时间
  - updateTime: 更新时间

- **customers 集合**：存储客户信息（更新会员信息结构）
  - name: 姓名
  - phone: 电话
  - joinDate: 加入时间
  - notes: 备注
  - membership: 会员信息
    - cardId: 会员卡产品ID
    - expireDate: 会员有效期
    - remainingTrainingCount: 剩余训练次数
    - purchaseDate: 购买日期
    - renewalHistory: 续费历史 [{ date, cardId, amount }]
  - createTime: 创建时间
  - updateTime: 更新时间

- **training_records 集合**：存储训练记录（保持不变）
  - customerId: 客户ID
  - type: 训练类型（eye/vision）
  - duration: 训练时长
  - content: 训练内容
  - createTime: 创建时间

- **purchase_records 集合**：存储购买记录（保持不变）
  - customerId: 客户ID
  - productId: 产品ID（可以是普通产品或会员卡）
  - amount: 金额
  - remark: 备注
  - createTime: 创建时间

## 云函数

### 当前云函数设计

- **customerService**：客户管理相关云函数
  - getCustomerList: 获取客户列表
  - getCustomerDetail: 获取客户详情
  - addCustomer: 添加客户
  - updateCustomer: 更新客户
  - deleteCustomer: 删除客户
  - getMembershipInfo: 获取客户会员信息
  - updateMembership: 更新客户会员信息

- **recordService**：记录管理相关云函数
  - addTrainingRecord: 添加训练记录
  - getTrainingRecords: 获取训练记录
  - addPurchaseRecord: 添加购买记录
  - getPurchaseRecords: 获取购买记录

- **productService**：产品管理相关云函数
  - getProductList: 获取产品列表
  - getProductDetail: 获取产品详情
  - addProduct: 添加产品
  - updateProduct: 更新产品
  - deleteProduct: 删除产品

- **membershipService**：会员卡管理相关云函数
  - getCardList: 获取会员卡列表
  - getCardDetail: 获取会员卡详情
  - addCard: 添加会员卡
  - updateCard: 更新会员卡
  - deleteCard: 删除会员卡
  - purchaseCard: 客户购买会员卡
  - renewCard: 客户续费会员卡

- **categoryService**：分类管理相关云函数
  - getCategoryList: 获取分类列表
  - getCategoryDetail: 获取分类详情
  - addCategory: 添加分类
  - updateCategory: 更新分类
  - deleteCategory: 删除分类

- **initDatabase**：数据库初始化云函数

### 新的云函数设计（会员卡作为特殊商品）

- **customerService**：客户管理相关云函数
  - getCustomerList: 获取客户列表
  - getCustomerDetail: 获取客户详情
  - addCustomer: 添加客户
  - updateCustomer: 更新客户
  - deleteCustomer: 删除客户
  - getMembershipInfo: 获取客户会员信息
  - updateMembership: 更新客户会员信息

- **recordService**：记录管理相关云函数（保持不变）
  - addTrainingRecord: 添加训练记录
  - getTrainingRecords: 获取训练记录
  - addPurchaseRecord: 添加购买记录
  - getPurchaseRecords: 获取购买记录

- **productService**：产品管理相关云函数（增强）
  - getProductList: 获取产品列表（增加type参数，可筛选会员卡）
  - getProductDetail: 获取产品详情
  - addProduct: 添加产品（支持会员卡类型）
  - updateProduct: 更新产品
  - deleteProduct: 删除产品
  - getMembershipProducts: 获取会员卡类型产品
  - calculateMemberPrice: 计算会员价格

- **membershipService**：会员管理相关云函数（修改）
  - purchaseMembershipCard: 客户购买会员卡
  - renewMembershipCard: 客户续费会员卡
  - getMemberPrivileges: 获取会员特权
  - checkMemberStatus: 检查会员状态

- **categoryService**：分类管理相关云函数
  - getCategoryList: 获取分类列表
  - getCategoryDetail: 获取分类详情
  - addCategory: 添加分类
  - updateCategory: 更新分类
  - deleteCategory: 删除分类

- **initDatabase**：数据库初始化云函数（更新）

## 使用说明

1. 首次使用时，需要初始化数据库，可以在云开发控制台手动触发 initDatabase 云函数
2. 小程序启动后，默认进入客户列表页
3. 可以通过底部导航栏切换不同功能页面
4. 在客户详情页可以查看客户的详细信息，并添加训练记录或购买记录
5. 在产品管理页可以管理产品信息

## 图标文件

需要准备以下图标文件，放置在 `miniprogram/images/icons/` 目录下：
- 客户图标 (customer.png 和 customer-active.png)
- 记录图标 (record.png 和 record-active.png)
- 产品图标 (product.png 和 product-active.png)

可以从图标库网站下载iOS风格的图标，例如：
- Iconfont (https://www.iconfont.cn/)
- Flaticon (https://www.flaticon.com/)
- Icons8 (https://icons8.com/)

## 会员卡重构后续任务

### 测试任务

1. **测试产品管理功能**
   - 测试添加普通产品
   - 测试添加会员卡产品（基础卡、高级卡、年度卡、定制卡）
   - 测试会员卡包含产品的管理
   - 测试编辑和删除产品

2. **测试会员卡管理功能**
   - 测试会员卡列表页面
   - 测试会员卡详情展示
   - 测试会员特权设置（折扣、积分倍数等）

3. **测试客户会员管理功能**
   - 测试客户购买会员卡
   - 测试客户续费会员卡
   - 测试客户详情页会员信息展示
   - 测试会员有效期和训练次数管理

4. **测试会员特权应用**
   - 测试会员折扣功能
   - 测试其他会员特权

### 优化任务

1. **用户体验优化**
   - 优化会员卡管理界面
   - 添加会员卡预览功能
   - 优化会员特权展示

2. **性能优化**
   - 优化数据加载速度
   - 添加数据缓存机制

### 清理任务

1. **代码清理**
   - 移除旧的会员卡管理页面
   - 整理冗余代码

2. **数据清理**
   - 备份membership_cards集合数据
   - 在确认所有功能正常后，清理不再使用的集合

## 长期优化方向

1. 添加用户登录功能，区分不同用户权限
2. 增加数据统计和分析功能
   - 会员卡销售统计
   - 会员续费率分析
   - 产品销售统计
3. 优化UI界面，添加更多交互效果
4. 增加图片上传功能，可以为客户或产品添加图片
5. 增加导出数据功能，可以导出客户信息或记录信息
6. 增加会员积分系统，与会员特权联动