# 客户信息管理小程序 - 测试指南

本文档提供了测试客户信息管理小程序各项功能的步骤和方法。

## 准备工作

1. 确保已部署所有云函数（参考 cloudfunctions/README.md）
2. 确保已初始化数据库
3. 确保小程序可以正常启动

## 测试流程

### 1. 客户管理功能测试

#### 1.1 客户列表页测试

- [ ] 打开小程序，默认进入客户列表页
- [ ] 验证客户列表是否正确显示
- [ ] 测试搜索功能：输入客户姓名，点击搜索
- [ ] 测试下拉刷新功能
- [ ] 测试上拉加载更多功能（如果有足够多的客户数据）
- [ ] 点击客户项，验证是否正确跳转到客户详情页
- [ ] 点击添加按钮（右下角+号），验证是否正确跳转到添加客户页

#### 1.2 客户详情页测试

- [ ] 从客户列表页点击客户，进入客户详情页
- [ ] 验证客户基本信息是否正确显示
- [ ] 验证客户训练记录是否正确显示
- [ ] 验证客户购买记录是否正确显示
- [ ] 点击"添加训练记录"按钮，验证是否正确跳转到添加记录页
- [ ] 点击"添加购买记录"按钮，验证是否正确跳转到添加记录页
- [ ] 点击"编辑客户"按钮，验证是否正确跳转到编辑客户页
- [ ] 点击"拨打电话"按钮，验证是否正确调用拨打电话功能

#### 1.3 添加/编辑客户页测试

- [ ] 从客户列表页点击添加按钮，进入添加客户页
- [ ] 测试表单验证：不填写必填字段，点击保存，验证是否有错误提示
- [ ] 填写客户信息，点击保存，验证是否成功添加客户
- [ ] 从客户详情页点击编辑按钮，进入编辑客户页
- [ ] 修改客户信息，点击保存，验证是否成功更新客户信息
- [ ] 点击取消按钮，验证是否正确返回上一页

### 2. 记录管理功能测试

#### 2.1 添加训练记录测试

- [ ] 从客户详情页点击"添加训练记录"按钮，进入添加记录页
- [ ] 验证客户信息是否正确预填
- [ ] 选择"训练记录"类型
- [ ] 选择训练类型（眼睛训练/视觉训练）
- [ ] 填写训练时长和内容
- [ ] 点击保存，验证是否成功添加训练记录
- [ ] 返回客户详情页，验证新添加的训练记录是否显示

#### 2.2 添加购买记录测试

- [ ] 从客户详情页点击"添加购买记录"按钮，进入添加记录页
- [ ] 验证客户信息是否正确预填
- [ ] 选择"购买记录"类型
- [ ] 选择产品
- [ ] 验证金额是否自动填充
- [ ] 填写备注信息
- [ ] 点击保存，验证是否成功添加购买记录
- [ ] 返回客户详情页，验证新添加的购买记录是否显示

### 3. 产品管理功能测试

#### 3.1 产品列表页测试

- [ ] 点击底部导航栏的"产品"，进入产品列表页
- [ ] 验证产品列表是否正确显示
- [ ] 测试搜索功能：输入产品名称，点击搜索
- [ ] 测试分类筛选功能：点击不同分类，验证是否正确筛选产品
- [ ] 测试下拉刷新功能
- [ ] 测试上拉加载更多功能（如果有足够多的产品数据）
- [ ] 点击添加按钮（右下角+号），验证是否正确跳转到添加产品页

#### 3.2 添加/编辑产品页测试

- [ ] 从产品列表页点击添加按钮，进入添加产品页
- [ ] 测试表单验证：不填写必填字段，点击保存，验证是否有错误提示
- [ ] 填写产品信息，点击保存，验证是否成功添加产品
- [ ] 从产品列表页点击产品的"编辑"按钮，进入编辑产品页
- [ ] 修改产品信息，点击保存，验证是否成功更新产品信息
- [ ] 点击取消按钮，验证是否正确返回上一页

#### 3.3 删除产品测试

- [ ] 从产品列表页点击产品的"删除"按钮
- [ ] 验证是否弹出确认对话框
- [ ] 点击"取消"，验证是否取消删除操作
- [ ] 再次点击"删除"按钮，然后点击"确认"，验证是否成功删除产品
- [ ] 验证产品列表是否更新

## 问题记录

在测试过程中发现的问题，请记录在下方：

1. 问题描述：
   - 页面：
   - 操作步骤：
   - 预期结果：
   - 实际结果：
   - 可能原因：

2. 问题描述：
   - 页面：
   - 操作步骤：
   - 预期结果：
   - 实际结果：
   - 可能原因：

## 测试结果

- [ ] 客户管理功能测试通过
- [ ] 记录管理功能测试通过
- [ ] 产品管理功能测试通过
