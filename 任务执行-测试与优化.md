# 会员卡重构 - 测试与优化任务执行计划

## 背景

会员卡重构项目已完成前三个阶段（部署云函数和数据迁移、更新核心前端页面、更新其他前端页面），现在需要进行第四阶段的全面测试和优化工作。

## 测试记录

测试过程和结果将记录在 [测试记录-会员卡重构.md](./测试记录-会员卡重构.md) 文件中。

## 测试任务

### 1. 产品管理功能测试

#### 1.1 普通产品测试
- [ ] 测试添加普通产品
  - [ ] 验证产品名称、价格、分类、描述等基本信息
  - [ ] 验证产品类型为"normal"
- [ ] 测试编辑普通产品
  - [ ] 验证修改后的信息正确保存
- [ ] 测试删除普通产品
  - [ ] 验证产品被成功删除
  - [ ] 验证相关购买记录不受影响

#### 1.2 会员卡产品测试
- [ ] 测试添加基础会员卡
  - [ ] 验证会员卡基本信息
  - [ ] 验证会员有效期设置
  - [ ] 验证包含训练次数设置
  - [ ] 验证会员特权设置（折扣等）
- [ ] 测试添加高级会员卡
  - [ ] 验证高级会员卡特有属性
- [ ] 测试添加年度会员卡
  - [ ] 验证年度会员卡特有属性
- [ ] 测试添加定制会员卡
  - [ ] 验证定制会员卡特有属性
- [ ] 测试会员卡包含产品的管理
  - [ ] 验证添加包含产品
  - [ ] 验证修改包含产品数量
  - [ ] 验证移除包含产品
- [ ] 测试编辑会员卡产品
  - [ ] 验证修改后的信息正确保存
- [ ] 测试删除会员卡产品
  - [ ] 验证产品被成功删除
  - [ ] 验证相关客户会员信息不受影响

### 2. 会员卡管理功能测试

- [ ] 测试会员卡列表页面
  - [ ] 验证只显示类型为"membership"的产品
  - [ ] 验证会员卡信息显示正确（名称、价格、有效期等）
  - [ ] 验证会员卡分类显示正确（基础卡、高级卡、年度卡、定制卡）
- [ ] 测试会员卡详情展示
  - [ ] 验证会员卡基本信息显示
  - [ ] 验证会员特权信息显示
  - [ ] 验证包含产品信息显示
- [ ] 测试会员特权设置
  - [ ] 验证折扣设置
  - [ ] 验证积分倍数设置
  - [ ] 验证其他特权设置

### 3. 客户会员管理功能测试

- [ ] 测试客户购买会员卡
  - [ ] 验证客户会员信息更新
  - [ ] 验证会员有效期计算正确
  - [ ] 验证剩余训练次数更新
  - [ ] 验证购买记录生成
- [ ] 测试客户续费会员卡
  - [ ] 验证会员有效期延长
  - [ ] 验证剩余训练次数累加
  - [ ] 验证续费记录生成
- [ ] 测试客户详情页会员信息展示
  - [ ] 验证会员卡信息显示
  - [ ] 验证会员有效期显示
  - [ ] 验证剩余训练次数显示
  - [ ] 验证会员特权显示
- [ ] 测试会员有效期和训练次数管理
  - [ ] 验证训练记录添加后训练次数减少
  - [ ] 验证会员有效期到期后状态变化

### 4. 会员特权应用测试

- [ ] 测试会员折扣功能
  - [ ] 验证会员购买普通产品时折扣应用
  - [ ] 验证折扣金额计算正确
- [ ] 测试其他会员特权
  - [ ] 验证积分倍数特权（如适用）
  - [ ] 验证其他特权功能

## 优化任务

### 1. 用户体验优化

- [ ] 优化会员卡管理界面
  - [ ] 改进会员卡列表页面布局
  - [ ] 优化会员卡详情页面展示
- [ ] 添加会员卡预览功能
  - [ ] 实现会员卡添加/编辑时的预览效果
- [ ] 优化会员特权展示
  - [ ] 使用图标或其他视觉元素展示特权
  - [ ] 添加特权说明提示

### 2. 性能优化

- [ ] 优化数据加载速度
  - [ ] 优化云函数查询逻辑
  - [ ] 减少不必要的数据请求
- [ ] 添加数据缓存机制
  - [ ] 缓存常用数据（如产品列表）
  - [ ] 实现数据更新时缓存刷新

## 清理任务

### 1. 代码清理

- [ ] 移除旧的会员卡管理页面
  - [ ] 确认新页面功能完善后移除旧页面
  - [ ] 更新相关导航和引用
- [ ] 整理冗余代码
  - [ ] 清理不再使用的函数和变量
  - [ ] 优化代码结构和注释

### 2. 数据清理

- [ ] 备份membership_cards集合数据
  - [ ] 确认数据已完全迁移到products集合
- [ ] 在确认所有功能正常后，清理不再使用的集合
  - [ ] 标记废弃集合
  - [ ] 在适当时机删除废弃集合

## 执行计划

1. 先完成全面测试，确保所有功能正常工作
2. 然后进行用户体验和性能优化
3. 最后进行代码和数据清理

## 已完成的优化

### 1. 分类管理功能
- [x] 创建了新的云函数 `categoryService`
- [x] 创建了分类管理页面（列表页和添加/编辑页）
- [x] 修改了产品添加/编辑页面，使其从数据库中动态获取分类信息
- [x] 在产品列表页面添加了分类管理入口

### 2. 导航错误修复
- [x] 修复了所有页面中使用 `wx.navigateBack()` 可能导致的错误
- [x] 添加了 `safeNavigateBack()` 方法，检查页面栈深度
- [x] 当页面栈只有一个页面时，使用 `wx.redirectTo()` 跳转到合适的页面

### 3. 数据库初始化优化
- [x] 修复了 `initDatabase` 云函数的集合已存在错误
- [x] 添加了集合存在性检查，避免重复创建集合
- [x] 添加了数据存在性检查，避免重复添加示例数据
- [x] 创建了专门的 `initCategories` 云函数用于初始化分类数据

### 4. 添加记录功能修复
- [x] 修复了添加记录后不返回的问题
- [x] 改进了 `safeNavigateBack()` 方法，根据客户ID智能选择返回目标
- [x] 修复了从底部导航栏添加记录后无法返回的问题
- [x] 正确处理 tabBar 页面的导航逻辑（使用 `wx.switchTab()` 和 `wx.navigateTo()`）
- [x] 修复了购买记录显示"未知产品"的问题
- [x] 在 `customerService.getCustomerDetail` 中添加了产品信息查询
- [x] 修复了客户详情页购买记录"+"按钮点击无效的问题
- [x] 添加了 `.card-action` 样式定义和调试信息
- [x] 创建了详细的测试指南用于验证修复效果

## 注意事项

1. 测试过程中发现的问题应立即修复
2. 优化应在确保功能正常的基础上进行
3. 代码和数据清理应谨慎进行，避免影响现有功能
4. 保持良好的文档记录，便于后续维护
