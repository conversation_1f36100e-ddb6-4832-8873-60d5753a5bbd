# 会员卡重构 - 测试记录

## 测试环境

- 微信开发者工具
- 云开发环境：客户信息管理小程序

## 1. 产品管理功能测试

### 1.1 普通产品测试

#### 添加普通产品
- **测试步骤**：
  1. 进入产品列表页
  2. 点击右下角"+"按钮
  3. 选择产品类型为"普通产品"
  4. 填写产品信息（名称、价格、分类、描述）
  5. 点击"保存"按钮
- **预期结果**：
  - 产品成功添加到数据库
  - 产品类型为"normal"
  - 产品列表页显示新添加的产品
- **实际结果**：
  - 待测试

#### 编辑普通产品
- **测试步骤**：
  1. 在产品列表页选择一个普通产品
  2. 点击"编辑"按钮
  3. 修改产品信息
  4. 点击"保存"按钮
- **预期结果**：
  - 产品信息成功更新
  - 产品列表页显示更新后的产品信息
- **实际结果**：
  - 待测试

#### 删除普通产品
- **测试步骤**：
  1. 在产品列表页选择一个普通产品
  2. 点击"删除"按钮
  3. 确认删除
- **预期结果**：
  - 产品成功从数据库中删除
  - 产品列表页不再显示该产品
- **实际结果**：
  - 待测试

### 1.2 会员卡产品测试

#### 添加基础会员卡
- **测试步骤**：
  1. 进入产品列表页
  2. 点击右下角"+"按钮
  3. 选择产品类型为"会员卡"
  4. 选择分类为"基础会员卡"
  5. 填写会员卡信息（名称、价格、有效期、训练次数等）
  6. 设置会员特权（折扣等）
  7. 点击"保存"按钮
- **预期结果**：
  - 会员卡成功添加到数据库
  - 产品类型为"membership"
  - 会员卡级别为"basic"
  - 会员卡列表页显示新添加的会员卡
- **实际结果**：
  - 待测试

#### 添加高级会员卡
- **测试步骤**：
  1. 进入产品列表页
  2. 点击右下角"+"按钮
  3. 选择产品类型为"会员卡"
  4. 选择分类为"高级会员卡"
  5. 填写会员卡信息（名称、价格、有效期、训练次数等）
  6. 设置会员特权（折扣等）
  7. 点击"保存"按钮
- **预期结果**：
  - 会员卡成功添加到数据库
  - 产品类型为"membership"
  - 会员卡级别为"premium"
  - 会员卡列表页显示新添加的会员卡
- **实际结果**：
  - 待测试

#### 添加年度会员卡
- **测试步骤**：
  1. 进入产品列表页
  2. 点击右下角"+"按钮
  3. 选择产品类型为"会员卡"
  4. 选择分类为"年度会员卡"
  5. 填写会员卡信息（名称、价格、有效期、训练次数等）
  6. 设置会员特权（折扣等）
  7. 点击"保存"按钮
- **预期结果**：
  - 会员卡成功添加到数据库
  - 产品类型为"membership"
  - 会员卡级别为"annual"
  - 会员卡列表页显示新添加的会员卡
- **实际结果**：
  - 待测试

#### 添加定制会员卡
- **测试步骤**：
  1. 进入产品列表页
  2. 点击右下角"+"按钮
  3. 选择产品类型为"会员卡"
  4. 选择分类为"定制会员卡"
  5. 填写会员卡信息（名称、价格、有效期、训练次数等）
  6. 设置会员特权（折扣等）
  7. 添加自定义权益
  8. 点击"保存"按钮
- **预期结果**：
  - 会员卡成功添加到数据库
  - 产品类型为"membership"
  - 会员卡级别为"custom"
  - 会员卡列表页显示新添加的会员卡
- **实际结果**：
  - 待测试

#### 会员卡包含产品管理
- **测试步骤**：
  1. 在添加/编辑会员卡页面
  2. 点击"添加包含产品"
  3. 选择产品并设置数量
  4. 保存会员卡
- **预期结果**：
  - 会员卡成功保存包含的产品信息
  - 会员卡详情页显示包含的产品
- **实际结果**：
  - 待测试

#### 编辑会员卡产品
- **测试步骤**：
  1. 在会员卡列表页选择一个会员卡
  2. 点击"编辑"按钮
  3. 修改会员卡信息
  4. 点击"保存"按钮
- **预期结果**：
  - 会员卡信息成功更新
  - 会员卡列表页显示更新后的会员卡信息
- **实际结果**：
  - 待测试

#### 删除会员卡产品
- **测试步骤**：
  1. 在会员卡列表页选择一个未被使用的会员卡
  2. 点击"删除"按钮
  3. 确认删除
- **预期结果**：
  - 会员卡成功从数据库中删除
  - 会员卡列表页不再显示该会员卡
- **实际结果**：
  - 待测试

## 2. 会员卡管理功能测试

### 会员卡列表页面
- **测试内容**：
  - 会员卡列表页是否只显示类型为"membership"的产品
  - 会员卡信息显示是否正确
  - 会员卡分类显示是否正确
- **预期结果**：
  - 只显示会员卡类型产品
  - 显示会员卡名称、价格、有效期等信息
  - 根据级别显示不同分类
- **实际结果**：
  - 待测试

### 会员卡详情展示
- **测试内容**：
  - 会员卡基本信息显示
  - 会员特权信息显示
  - 包含产品信息显示
- **预期结果**：
  - 显示会员卡基本信息
  - 显示会员特权信息
  - 显示包含产品信息
- **实际结果**：
  - 待测试

## 3. 客户会员管理功能测试

### 客户购买会员卡
- **测试步骤**：
  1. 进入客户详情页
  2. 点击"添加记录"
  3. 选择"购买记录"
  4. 选择会员卡产品
  5. 填写购买信息
  6. 点击"保存"按钮
- **预期结果**：
  - 客户会员信息更新
  - 会员有效期计算正确
  - 剩余训练次数更新
  - 购买记录生成
- **实际结果**：
  - 待测试

### 客户续费会员卡
- **测试步骤**：
  1. 进入已有会员卡的客户详情页
  2. 点击"续费会员卡"
  3. 选择会员卡
  4. 填写续费信息
  5. 点击"保存"按钮
- **预期结果**：
  - 会员有效期延长
  - 剩余训练次数累加
  - 续费记录生成
- **实际结果**：
  - 待测试

## 4. 会员特权应用测试

### 会员折扣功能
- **测试步骤**：
  1. 进入有会员卡的客户详情页
  2. 点击"添加记录"
  3. 选择"购买记录"
  4. 选择普通产品
  5. 查看价格是否应用折扣
- **预期结果**：
  - 会员购买普通产品时应用折扣
  - 折扣金额计算正确
- **实际结果**：
  - 待测试
