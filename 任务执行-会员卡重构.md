# 会员卡重构任务执行计划

## 背景

将会员卡作为特殊商品类型进行设计，统一管理产品和会员卡，同时保留会员卡的特殊属性。这样可以更灵活地设置会员卡属性和权益，实现会员折扣等功能。

## 分阶段实施计划

### 第一阶段：部署云函数和数据迁移（已完成）
- [x] 修改云函数代码
- [x] 创建数据迁移脚本
- [x] 部署云函数
- [x] 执行数据迁移
- [x] 验证数据迁移结果

### 第二阶段：更新核心前端页面（已完成）
- [x] 修改会员卡管理相关页面
- [x] 修改客户会员管理相关页面

### 第三阶段：更新其他前端页面（已完成）
- [x] 修改产品管理相关页面
  - [x] 添加产品类型选择
  - [x] 添加会员卡特有字段
  - [x] 添加会员卡包含产品的管理功能
  - [x] 细化会员卡分类
- [x] 修改客户添加/编辑页面（已通过会员卡重构适配）

### 第四阶段：全面测试和优化（进行中）
- [ ] 测试所有功能
  - [ ] 测试产品管理功能
  - [ ] 测试会员卡管理功能
  - [ ] 测试客户会员管理功能
  - [ ] 测试会员特权应用
- [ ] 优化用户体验和性能
- [ ] 清理旧代码和数据
  - [ ] 移除旧的会员卡管理页面
  - [ ] 清理不再使用的数据库集合

> 详细测试与优化计划已创建，请参考 [任务执行-测试与优化.md](./任务执行-测试与优化.md)

## 原任务概述

1. 修改数据库结构
2. 更新产品管理功能
3. 修改会员卡管理功能
4. 更新客户会员管理
5. 数据迁移
6. 测试和验证

## 详细任务计划

### 1. 修改数据库结构

- [x] 更新README.md，添加新的数据库设计方案
- [x] 修改产品集合结构，添加type字段和membershipInfo子文档
- [x] 修改客户集合结构，更新会员信息存储方式

### 2. 更新产品管理功能

- [x] 修改productService云函数
  - [x] 更新getProductList函数，支持按type筛选
  - [x] 修改addProduct和updateProduct函数，支持会员卡特有属性
  - [x] 添加getMembershipProducts函数
  - [x] 添加calculateMemberPrice函数

- [x] 修改产品添加/编辑页面
  - [x] 添加产品类型选择
  - [x] 根据产品类型显示不同表单字段
  - [x] 添加会员卡特权设置表单

- [x] 修改产品列表页面
  - [x] 添加产品类型筛选
  - [x] 显示会员卡特有信息

### 3. 修改会员卡管理功能

- [x] 修改membershipService云函数
  - [x] 移除getCardList、getCardDetail、addCard、updateCard、deleteCard函数
  - [x] 修改purchaseCard和renewCard函数，适配新的数据结构
  - [x] 添加getMemberPrivileges和checkMemberStatus函数

- [x] 修改会员卡列表页面
  - [x] 调用productService.getProductList获取会员卡类型产品
  - [x] 更新UI显示

- [x] 修改会员卡添加/编辑页面
  - [x] 重定向到产品添加/编辑页面，预设type为'membership'

### 4. 更新客户会员管理

- [x] 修改customerService云函数
  - [x] 更新getMembershipInfo函数，适配新的数据结构
  - [x] 修改updateMembership函数

- [x] 修改客户详情页面
  - [x] 更新会员信息显示
  - [x] 显示会员特权信息

- [x] 修改客户添加/编辑页面
  - [x] 更新会员卡选择逻辑

### 5. 数据迁移

- [x] 创建数据迁移脚本
  - [x] 将membership_cards集合数据迁移到products集合
  - [x] 更新客户的会员卡引用
  - [x] 更新购买记录中的会员卡引用

### 6. 测试和验证

- [ ] 测试产品管理功能
  - [ ] 添加普通产品
  - [ ] 添加会员卡产品
  - [ ] 编辑产品
  - [ ] 删除产品

- [ ] 测试会员卡功能
  - [ ] 客户购买会员卡
  - [ ] 客户续费会员卡
  - [ ] 会员特权应用

- [ ] 测试客户会员管理
  - [ ] 查看客户会员信息
  - [ ] 更新客户会员信息

## 执行顺序

1. 先完成数据库结构修改和云函数更新
2. 然后更新前端页面
3. 创建并执行数据迁移脚本
4. 最后进行全面测试

## 风险和注意事项

1. 数据迁移过程中需要注意数据一致性
2. 确保向后兼容，不影响现有功能
3. 需要全面测试会员卡购买、续费等核心功能
4. 可能需要临时维护两套逻辑，直到迁移完成
