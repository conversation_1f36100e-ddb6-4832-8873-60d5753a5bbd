# 云函数部署指南

## 概述

本指南将帮助您部署新增的云函数和修复的功能。

## 需要部署的云函数

### 1. categoryService（新增）
**功能**：分类管理服务
**位置**：`cloudfunctions/categoryService/`
**状态**：新增，需要部署

### 2. initCategories（新增）
**功能**：专门用于初始化分类数据
**位置**：`cloudfunctions/initCategories/`
**状态**：新增，需要部署

### 3. initDatabase（修复）
**功能**：数据库初始化服务
**位置**：`cloudfunctions/initDatabase/`
**状态**：已修复，需要重新部署

## 部署步骤

### 步骤1：部署 categoryService 云函数

1. 在微信开发者工具中，右键点击 `cloudfunctions/categoryService` 文件夹
2. 选择"上传并部署：云端安装依赖（不上传node_modules）"
3. 等待部署完成

### 步骤2：部署 initCategories 云函数

1. 在微信开发者工具中，右键点击 `cloudfunctions/initCategories` 文件夹
2. 选择"上传并部署：云端安装依赖（不上传node_modules）"
3. 等待部署完成

### 步骤3：重新部署 initDatabase 云函数

1. 在微信开发者工具中，右键点击 `cloudfunctions/initDatabase` 文件夹
2. 选择"上传并部署：云端安装依赖（不上传node_modules）"
3. 等待部署完成

## 初始化数据库

### 方案1：使用修复后的 initDatabase 云函数

1. 在微信开发者工具的云开发控制台中，找到 `initDatabase` 云函数
2. 点击"测试"按钮
3. 不需要传入任何参数，直接点击"调用"
4. 查看返回结果，应该显示：
   ```json
   {
     "success": true,
     "message": "数据库初始化成功",
     "details": [
       "customers 集合已存在",
       "training_records 集合已存在",
       "products 集合已存在",
       "purchase_records 集合已存在",
       "membership_cards 集合已存在",
       "categories 集合创建成功",
       "产品 数据已存在，跳过添加",
       "客户 数据已存在，跳过添加",
       "会员卡 数据已存在，跳过添加",
       "分类 示例数据添加成功"
     ]
   }
   ```

### 方案2：使用专门的 initCategories 云函数

如果您只想初始化分类数据：

1. 在微信开发者工具的云开发控制台中，找到 `initCategories` 云函数
2. 点击"测试"按钮
3. 不需要传入任何参数，直接点击"调用"
4. 查看返回结果，应该显示：
   ```json
   {
     "success": true,
     "message": "分类数据初始化成功",
     "details": [
       "categories 集合已存在",
       "成功添加 8 个分类"
     ],
     "addedCount": 8
   }
   ```

## 验证部署结果

### 1. 验证 categoryService 云函数

在微信开发者工具的调试器中运行以下代码：

```javascript
wx.cloud.callFunction({
  name: 'categoryService',
  data: {
    action: 'getCategoryList',
    data: {
      page: 1,
      pageSize: 10
    }
  }
}).then(res => {
  console.log('分类列表:', res.result)
}).catch(err => {
  console.error('调用失败:', err)
})
```

### 2. 验证分类管理页面

1. 在小程序中导航到产品列表页
2. 点击右下角的"分类"按钮
3. 应该能够看到分类列表页面
4. 尝试添加、编辑和删除分类

### 3. 验证产品添加页面的分类选择

1. 在产品列表页点击"+"按钮
2. 在产品类型中选择"普通产品"
3. 分类选择器应该显示从数据库中获取的分类
4. 在产品类型中选择"会员卡"
5. 分类选择器应该显示会员卡相关的分类

## 常见问题

### Q1: categoryService 云函数调用失败
**解决方案**：
1. 检查云函数是否部署成功
2. 检查云函数权限设置
3. 查看云函数日志，确认错误原因

### Q2: 分类数据没有显示
**解决方案**：
1. 确认 initDatabase 或 initCategories 云函数执行成功
2. 在云开发控制台检查 categories 集合是否存在数据
3. 检查前端页面的 categoryService 调用是否正确

### Q3: 产品添加页面分类选择器为空
**解决方案**：
1. 检查产品添加页面的 loadCategories 方法是否正确调用
2. 确认 categoryService 云函数返回数据正确
3. 检查前端的分类数据处理逻辑

## 回滚方案

如果部署后出现问题，可以：

1. **回滚云函数**：在云开发控制台的云函数管理中，选择之前的版本进行回滚
2. **删除分类集合**：如果分类数据有问题，可以在数据库中删除 categories 集合，然后重新初始化
3. **恢复前端代码**：如果前端页面有问题，可以通过版本控制系统恢复到之前的版本

## 注意事项

1. 部署前请确保备份重要数据
2. 建议先在测试环境中验证功能正常后再部署到生产环境
3. 部署完成后请及时测试所有相关功能
4. 如果遇到问题，请查看云函数日志获取详细错误信息
