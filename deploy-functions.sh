#!/bin/bash

# 部署云函数脚本
# 使用方法: ./deploy-functions.sh [函数名1] [函数名2] ...
# 如果不指定函数名，则部署所有云函数

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 云函数目录
FUNCTIONS_DIR="cloudfunctions"

# 检查是否安装了微信开发者工具CLI
if ! command -v wxcli &> /dev/null; then
    echo -e "${RED}错误: 未找到微信开发者工具CLI${NC}"
    echo "请先安装微信开发者工具CLI，或确保其在PATH中"
    exit 1
fi

# 部署指定的云函数
deploy_function() {
    local func_name=$1
    echo -e "${YELLOW}正在部署云函数: ${func_name}${NC}"
    
    # 检查云函数目录是否存在
    if [ ! -d "${FUNCTIONS_DIR}/${func_name}" ]; then
        echo -e "${RED}错误: 云函数 ${func_name} 目录不存在${NC}"
        return 1
    fi
    
    # 进入云函数目录
    cd "${FUNCTIONS_DIR}/${func_name}" || return 1
    
    # 安装依赖
    echo "安装依赖..."
    npm install
    
    # 部署云函数
    echo "部署云函数..."
    wxcli cloud functions:deploy "${func_name}"
    
    # 返回上级目录
    cd ../..
    
    echo -e "${GREEN}云函数 ${func_name} 部署完成${NC}"
    return 0
}

# 主函数
main() {
    # 如果指定了函数名，则只部署指定的函数
    if [ $# -gt 0 ]; then
        for func_name in "$@"; do
            deploy_function "${func_name}"
        done
    else
        # 否则部署所有云函数
        echo -e "${YELLOW}正在部署所有云函数...${NC}"
        
        # 获取所有云函数目录
        for func_dir in "${FUNCTIONS_DIR}"/*; do
            if [ -d "${func_dir}" ]; then
                func_name=$(basename "${func_dir}")
                deploy_function "${func_name}"
            fi
        done
    fi
    
    echo -e "${GREEN}所有云函数部署完成${NC}"
}

# 执行主函数
main "$@"
