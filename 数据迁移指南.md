# 会员卡数据迁移指南

本文档提供了将会员卡数据从独立集合迁移到产品集合的详细步骤。

## 准备工作

1. 确保已部署以下云函数：
   - productService（已更新）
   - membershipService（已更新）
   - customerService（已更新）
   - migrateData（新增）

2. 确保您有数据库的管理权限

## 迁移步骤

### 1. 部署云函数

在微信开发者工具中，依次部署以下云函数：

1. productService
2. membershipService
3. customerService
4. migrateData

部署方法：
- 在微信开发者工具中，右键点击云函数目录
- 选择"上传并部署：云端安装依赖"
- 等待部署完成

### 2. 执行数据迁移

#### 第一步：迁移会员卡数据

1. 在微信开发者工具的云开发控制台中，选择"云函数"
2. 找到并点击"migrateData"云函数
3. 在"测试"面板中，输入以下测试数据：
   ```json
   {
     "action": "migrateMembershipCards"
   }
   ```
4. 点击"运行"按钮
5. 检查运行结果，确保所有会员卡都已成功迁移

#### 第二步：更新客户会员卡引用

1. 在微信开发者工具的云开发控制台中，选择"云函数"
2. 找到并点击"migrateData"云函数
3. 在"测试"面板中，输入以下测试数据：
   ```json
   {
     "action": "updateCustomerMembership"
   }
   ```
4. 点击"运行"按钮
5. 检查运行结果，确保所有客户的会员卡引用都已成功更新

### 3. 验证数据迁移

#### 检查产品集合

1. 在云开发控制台中，选择"数据库"
2. 查看"products"集合
3. 确认会员卡数据已成功迁移，并且包含以下字段：
   - type: 'membership'
   - membershipInfo: 包含会员卡特有属性

#### 检查客户集合

1. 在云开发控制台中，选择"数据库"
2. 查看"customers"集合
3. 确认客户的会员信息已更新为新格式：
   - membership.cardId: 会员卡产品ID
   - membership.expireDate: 会员有效期
   - membership.remainingTrainingCount: 剩余训练次数

## 回滚计划

如果迁移过程中出现问题，可以按照以下步骤回滚：

1. 恢复原始云函数：
   - 从备份中恢复原始的productService、membershipService和customerService云函数

2. 恢复客户数据：
   - 使用数据库管理工具，将客户集合恢复到迁移前的状态

## 注意事项

1. 迁移过程中，建议暂停使用小程序，避免数据不一致
2. 迁移完成后，需要更新前端页面，适配新的数据结构
3. 在所有前端页面更新完成前，部分功能可能无法正常使用
