// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event

  switch (action) {
    case 'getCategoryList':
      return getCategoryList(data)
    case 'getCategoryDetail':
      return getCategoryDetail(data)
    case 'addCategory':
      return addCategory(data)
    case 'updateCategory':
      return updateCategory(data)
    case 'deleteCategory':
      return deleteCategory(data)
    default:
      return {
        success: false,
        message: '未知操作'
      }
  }
}

// 获取分类列表
async function getCategoryList(data) {
  const { type, page = 1, pageSize = 10 } = data || {}
  
  try {
    // 构建查询条件
    const condition = {}
    if (type) {
      condition.type = type
    }
    
    // 查询总数
    const countResult = await db.collection('categories')
      .where(condition)
      .count()
    
    const total = countResult.total
    
    // 查询数据
    const result = await db.collection('categories')
      .where(condition)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .orderBy('createTime', 'desc')
      .get()
    
    return {
      success: true,
      data: result.data,
      total,
      page,
      pageSize
    }
  } catch (error) {
    return {
      success: false,
      message: '获取分类列表失败',
      error
    }
  }
}

// 获取分类详情
async function getCategoryDetail(data) {
  const { categoryId } = data
  
  try {
    const result = await db.collection('categories').doc(categoryId).get()
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    return {
      success: false,
      message: '获取分类详情失败',
      error
    }
  }
}

// 添加分类
async function addCategory(data) {
  try {
    // 检查是否已存在同名分类
    const existingCategory = await db.collection('categories')
      .where({
        name: data.name,
        type: data.type
      })
      .get()
    
    if (existingCategory.data.length > 0) {
      return {
        success: false,
        message: '已存在同名分类'
      }
    }
    
    // 添加创建时间
    data.createTime = db.serverDate()
    data.updateTime = db.serverDate()
    
    const result = await db.collection('categories').add({
      data
    })
    
    return {
      success: true,
      message: '添加分类成功',
      _id: result._id
    }
  } catch (error) {
    return {
      success: false,
      message: '添加分类失败',
      error
    }
  }
}

// 更新分类
async function updateCategory(data) {
  const { _id, ...updateData } = data
  
  try {
    // 检查是否已存在同名分类（排除自身）
    const existingCategory = await db.collection('categories')
      .where({
        name: updateData.name,
        type: updateData.type,
        _id: _.neq(_id)
      })
      .get()
    
    if (existingCategory.data.length > 0) {
      return {
        success: false,
        message: '已存在同名分类'
      }
    }
    
    // 添加更新时间
    updateData.updateTime = db.serverDate()
    
    await db.collection('categories').doc(_id).update({
      data: updateData
    })
    
    return {
      success: true,
      message: '更新分类成功'
    }
  } catch (error) {
    return {
      success: false,
      message: '更新分类失败',
      error
    }
  }
}

// 删除分类
async function deleteCategory(data) {
  const { categoryId } = data
  
  try {
    // 检查是否有产品使用该分类
    const products = await db.collection('products')
      .where({
        category: categoryId
      })
      .count()
    
    if (products.total > 0) {
      return {
        success: false,
        message: '该分类下有产品，无法删除'
      }
    }
    
    await db.collection('categories').doc(categoryId).remove()
    
    return {
      success: true,
      message: '删除分类成功'
    }
  } catch (error) {
    return {
      success: false,
      message: '删除分类失败',
      error
    }
  }
}
