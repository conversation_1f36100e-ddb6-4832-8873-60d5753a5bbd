// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event

  switch (action) {
    case 'getProductList':
      return getProductList(data)
    case 'getProductDetail':
      return getProductDetail(data)
    case 'addProduct':
      return addProduct(data)
    case 'updateProduct':
      return updateProduct(data)
    case 'deleteProduct':
      return deleteProduct(data)
    case 'getMembershipProducts':
      return getMembershipProducts(data)
    case 'calculateMemberPrice':
      return calculateMemberPrice(data)
    default:
      return {
        success: false,
        message: '未知操作'
      }
  }
}

// 获取产品列表
async function getProductList(data) {
  const { keyword = '', category = '', type = '', page = 1, pageSize = 10 } = data

  try {
    // 构建查询条件
    let query = {}
    if (keyword) {
      query.name = db.RegExp({
        regexp: keyword,
        options: 'i',
      })
    }

    if (category) {
      query.category = category
    }

    // 根据产品类型筛选
    if (type) {
      query.type = type
    }

    // 计算总数
    const countResult = await db.collection('products').where(query).count()
    const total = countResult.total

    // 查询产品列表
    const products = await db.collection('products')
      .where(query)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    return {
      success: true,
      data: products.data,
      total,
      page,
      pageSize
    }
  } catch (error) {
    return {
      success: false,
      message: '获取产品列表失败',
      error
    }
  }
}

// 获取产品详情
async function getProductDetail(data) {
  const { productId } = data

  try {
    const product = await db.collection('products').doc(productId).get()

    if (!product.data) {
      return {
        success: false,
        message: '产品不存在'
      }
    }

    return {
      success: true,
      data: product.data
    }
  } catch (error) {
    return {
      success: false,
      message: '获取产品详情失败',
      error
    }
  }
}

// 添加产品
async function addProduct(data) {
  try {
    // 设置默认值
    if (!data.type) {
      data.type = 'normal'; // 默认为普通产品
    }

    // 如果是会员卡类型，确保membershipInfo存在
    if (data.type === 'membership' && !data.membershipInfo) {
      data.membershipInfo = {
        duration: data.duration || 1,
        trainingCount: data.trainingCount || 0,
        level: data.level || 'basic',
        privileges: data.privileges || {
          discount: 1.0,
          pointsMultiplier: 1.0,
          freeShipping: false,
          priorityService: false
        },
        benefits: data.benefits || [],
        includedProducts: data.includedProducts || []
      };

      // 删除冗余字段
      delete data.duration;
      delete data.trainingCount;
      delete data.level;
      delete data.privileges;
      delete data.benefits;
      delete data.includedProducts;
    }

    const result = await db.collection('products').add({
      data: {
        ...data,
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    })

    return {
      success: true,
      message: '添加产品成功',
      data: {
        _id: result._id
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '添加产品失败',
      error
    }
  }
}

// 更新产品
async function updateProduct(data) {
  const { _id, ...updateData } = data

  try {
    // 如果是会员卡类型，确保membershipInfo结构正确
    if (updateData.type === 'membership') {
      // 如果直接提供了membershipInfo字段，使用它
      if (!updateData.membershipInfo) {
        updateData.membershipInfo = {
          duration: updateData.duration || 1,
          trainingCount: updateData.trainingCount || 0,
          level: updateData.level || 'basic',
          privileges: updateData.privileges || {
            discount: 1.0,
            pointsMultiplier: 1.0,
            freeShipping: false,
            priorityService: false
          },
          benefits: updateData.benefits || [],
          includedProducts: updateData.includedProducts || []
        };

        // 删除冗余字段
        delete updateData.duration;
        delete updateData.trainingCount;
        delete updateData.level;
        delete updateData.privileges;
        delete updateData.benefits;
        delete updateData.includedProducts;
      }
    }

    await db.collection('products').doc(_id).update({
      data: {
        ...updateData,
        updateTime: db.serverDate()
      }
    })

    return {
      success: true,
      message: '更新产品成功'
    }
  } catch (error) {
    return {
      success: false,
      message: '更新产品失败',
      error
    }
  }
}

// 删除产品
async function deleteProduct(data) {
  const { productId } = data

  try {
    // 检查产品类型，如果是会员卡，需要检查是否有客户正在使用
    const product = await db.collection('products').doc(productId).get()

    if (product.data && product.data.type === 'membership') {
      // 检查是否有客户正在使用该会员卡
      const customers = await db.collection('customers')
        .where({
          'membership.cardId': productId
        })
        .count()

      if (customers.total > 0) {
        return {
          success: false,
          message: '该会员卡正在被使用，无法删除'
        }
      }
    }

    await db.collection('products').doc(productId).remove()

    return {
      success: true,
      message: '删除产品成功'
    }
  } catch (error) {
    return {
      success: false,
      message: '删除产品失败',
      error
    }
  }
}

// 获取会员卡类型产品
async function getMembershipProducts(data) {
  // 直接调用getProductList，设置type为membership
  return getProductList({
    ...data,
    type: 'membership'
  })
}

// 计算会员价格
async function calculateMemberPrice(data) {
  const { customerId, productId } = data

  try {
    // 获取产品信息
    const product = await db.collection('products').doc(productId).get()
    if (!product.data) {
      return { success: false, message: '产品不存在' }
    }

    // 获取客户信息
    const customer = await db.collection('customers').doc(customerId).get()
    if (!customer.data) {
      return { success: false, message: '客户不存在' }
    }

    let price = product.data.price
    let discount = 1.0 // 默认无折扣
    let isMember = false
    let memberLevel = null

    // 检查客户是否是会员
    if (customer.data.membership &&
        customer.data.membership.expireDate &&
        new Date(customer.data.membership.expireDate) > new Date()) {

      isMember = true

      // 获取会员卡信息
      const membershipCard = await db.collection('products').doc(customer.data.membership.cardId).get()

      if (membershipCard.data &&
          membershipCard.data.type === 'membership' &&
          membershipCard.data.membershipInfo &&
          membershipCard.data.membershipInfo.privileges) {

        // 应用会员折扣
        discount = membershipCard.data.membershipInfo.privileges.discount || 1.0
        memberLevel = membershipCard.data.membershipInfo.level

        // 计算折扣价格
        price = price * discount
      }
    }

    return {
      success: true,
      data: {
        originalPrice: product.data.price,
        memberPrice: price,
        discount: discount,
        isMember: isMember,
        memberLevel: memberLevel
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '计算会员价格失败',
      error
    }
  }
}
