// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const results = []
    
    // 创建categories集合（如果不存在）
    try {
      await db.createCollection('categories')
      results.push('categories 集合创建成功')
    } catch (error) {
      if (error.errCode === -501001) {
        // 集合已存在
        results.push('categories 集合已存在')
      } else {
        throw error
      }
    }
    
    // 检查是否已有分类数据
    const count = await db.collection('categories').count()
    
    if (count.total > 0) {
      return {
        success: true,
        message: '分类数据已存在，无需初始化',
        details: results,
        existingCount: count.total
      }
    }
    
    // 添加示例分类
    const categoriesData = [
      {
        name: '眼镜',
        type: 'normal', // 普通产品分类
        description: '各类矫正眼镜',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '训练工具',
        type: 'normal', // 普通产品分类
        description: '视力训练相关工具',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '保健品',
        type: 'normal', // 普通产品分类
        description: '眼睛保健相关产品',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '其他',
        type: 'normal', // 普通产品分类
        description: '其他类型产品',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '基础会员卡',
        type: 'membership', // 会员卡分类
        description: '基础级别会员卡',
        level: 'basic',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '高级会员卡',
        type: 'membership', // 会员卡分类
        description: '高级级别会员卡',
        level: 'premium',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '年度会员卡',
        type: 'membership', // 会员卡分类
        description: '年度级别会员卡',
        level: 'annual',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '定制会员卡',
        type: 'membership', // 会员卡分类
        description: '定制级别会员卡',
        level: 'custom',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    ]
    
    const result = await db.collection('categories').add({
      data: categoriesData
    })
    
    results.push(`成功添加 ${categoriesData.length} 个分类`)
    
    return {
      success: true,
      message: '分类数据初始化成功',
      details: results,
      addedCount: categoriesData.length
    }
  } catch (error) {
    return {
      success: false,
      message: '分类数据初始化失败',
      error
    }
  }
}
