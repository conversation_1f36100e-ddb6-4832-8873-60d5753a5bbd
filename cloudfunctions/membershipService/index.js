// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event

  switch (action) {
    // 新的会员卡管理函数
    case 'purchaseMembershipCard':
      return purchaseMembershipCard(data)
    case 'renewMembershipCard':
      return renewMembershipCard(data)
    case 'getMemberPrivileges':
      return getMemberPrivileges(data)
    case 'checkMemberStatus':
      return checkMemberStatus(data)

    // 兼容旧版本的函数（重定向到新函数）
    case 'purchaseCard':
      return purchaseMembershipCard(data)
    case 'renewCard':
      return renewMembershipCard(data)

    // 已废弃的函数（重定向到productService）
    case 'getCardList':
      return redirectToProductService('getMembershipProducts', data)
    case 'getCardDetail':
      return redirectToProductService('getProductDetail', data)
    case 'addCard':
      return redirectToProductService('addProduct', { ...data, type: 'membership' })
    case 'updateCard':
      return redirectToProductService('updateProduct', { ...data, type: 'membership' })
    case 'deleteCard':
      return redirectToProductService('deleteProduct', { productId: data.cardId })

    default:
      return {
        success: false,
        message: '未知操作'
      }
  }
}

// 重定向到productService云函数
async function redirectToProductService(action, data) {
  try {
    // 调用productService云函数
    const result = await cloud.callFunction({
      name: 'productService',
      data: {
        action,
        data
      }
    })

    return result.result
  } catch (error) {
    return {
      success: false,
      message: `调用productService.${action}失败`,
      error
    }
  }
}

// 检查会员状态
async function checkMemberStatus(data) {
  const { customerId } = data

  try {
    // 获取客户信息
    const customer = await db.collection('customers').doc(customerId).get()

    if (!customer.data) {
      return {
        success: false,
        message: '客户不存在'
      }
    }

    // 检查会员是否有效
    let isMember = false
    let expireDate = null
    let remainingDays = 0
    let membershipCard = null

    if (customer.data.membership &&
        customer.data.membership.expireDate &&
        customer.data.membership.cardId) {

      expireDate = new Date(customer.data.membership.expireDate)
      const now = new Date()

      if (expireDate > now) {
        isMember = true

        // 计算剩余天数
        const diffTime = expireDate.getTime() - now.getTime()
        remainingDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

        // 获取会员卡信息
        const card = await db.collection('products').doc(customer.data.membership.cardId).get()
        if (card.data && card.data.type === 'membership') {
          membershipCard = card.data
        }
      }
    }

    return {
      success: true,
      data: {
        isMember,
        expireDate,
        remainingDays,
        remainingTrainingCount: customer.data.membership ? customer.data.membership.remainingTrainingCount : 0,
        membershipCard
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '检查会员状态失败',
      error
    }
  }
}

// 获取会员特权
async function getMemberPrivileges(data) {
  const { customerId } = data

  try {
    // 先检查会员状态
    const statusResult = await checkMemberStatus({ customerId })

    if (!statusResult.success) {
      return statusResult
    }

    const { isMember, membershipCard } = statusResult.data

    if (!isMember || !membershipCard) {
      return {
        success: true,
        data: {
          isMember: false,
          privileges: null
        }
      }
    }

    // 返回会员特权
    return {
      success: true,
      data: {
        isMember: true,
        memberLevel: membershipCard.membershipInfo.level,
        privileges: membershipCard.membershipInfo.privileges,
        benefits: membershipCard.membershipInfo.benefits
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '获取会员特权失败',
      error
    }
  }
}

// 客户购买会员卡（新版本）
async function purchaseMembershipCard(data) {
  const { customerId, cardId, paymentAmount } = data

  try {
    // 获取会员卡信息（从products集合中获取）
    const card = await db.collection('products').doc(cardId).get()

    if (!card.data || card.data.type !== 'membership') {
      return {
        success: false,
        message: '会员卡不存在或产品类型错误'
      }
    }

    // 获取会员卡特有信息
    const membershipInfo = card.data.membershipInfo
    if (!membershipInfo) {
      return {
        success: false,
        message: '会员卡信息不完整'
      }
    }

    // 计算会员有效期
    const now = new Date()
    const expireDate = new Date(now)
    expireDate.setMonth(expireDate.getMonth() + membershipInfo.duration)

    // 构建会员信息
    const membershipData = {
      cardId: cardId,
      expireDate: expireDate,
      remainingTrainingCount: membershipInfo.trainingCount,
      purchaseDate: db.serverDate(),
      renewalHistory: [{
        date: db.serverDate(),
        cardId: cardId,
        amount: paymentAmount || card.data.price
      }]
    }

    // 更新客户信息
    await db.collection('customers').doc(customerId).update({
      data: {
        membership: membershipData,
        updateTime: db.serverDate()
      }
    })

    // 添加购买记录
    await db.collection('purchase_records').add({
      data: {
        customerId,
        productId: cardId,
        amount: paymentAmount || card.data.price,
        remark: `购买会员卡：${card.data.name}`,
        createTime: db.serverDate()
      }
    })

    return {
      success: true,
      message: '购买会员卡成功',
      data: {
        expireDate: expireDate,
        membershipLevel: membershipInfo.level
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '购买会员卡失败',
      error
    }
  }
}

// 客户续费会员卡（新版本）
async function renewMembershipCard(data) {
  const { customerId, cardId, paymentAmount } = data

  try {
    // 获取会员卡信息（从products集合中获取）
    const card = await db.collection('products').doc(cardId).get()

    if (!card.data || card.data.type !== 'membership') {
      return {
        success: false,
        message: '会员卡不存在或产品类型错误'
      }
    }

    // 获取会员卡特有信息
    const membershipInfo = card.data.membershipInfo
    if (!membershipInfo) {
      return {
        success: false,
        message: '会员卡信息不完整'
      }
    }

    // 获取客户信息
    const customer = await db.collection('customers').doc(customerId).get()

    if (!customer.data) {
      return {
        success: false,
        message: '客户不存在'
      }
    }

    // 计算新的会员有效期
    let expireDate
    if (customer.data.membership &&
        customer.data.membership.expireDate &&
        new Date(customer.data.membership.expireDate) > new Date()) {
      // 如果当前会员卡还未过期，则在当前有效期基础上延长
      expireDate = new Date(customer.data.membership.expireDate)
      expireDate.setMonth(expireDate.getMonth() + membershipInfo.duration)
    } else {
      // 如果当前会员卡已过期，则从当前日期开始计算
      expireDate = new Date()
      expireDate.setMonth(expireDate.getMonth() + membershipInfo.duration)
    }

    // 构建续费记录
    const renewalRecord = {
      date: db.serverDate(),
      cardId: cardId,
      amount: paymentAmount || card.data.price
    }

    // 构建更新数据
    const updateData = {
      'membership.cardId': cardId,
      'membership.expireDate': expireDate,
      'membership.remainingTrainingCount': ((customer.data.membership && customer.data.membership.remainingTrainingCount) || 0) + membershipInfo.trainingCount,
      updateTime: db.serverDate()
    }

    // 添加续费记录
    if (customer.data.membership && customer.data.membership.renewalHistory) {
      updateData['membership.renewalHistory'] = db.command.push(renewalRecord)
    } else {
      updateData['membership.renewalHistory'] = [renewalRecord]
    }

    // 更新客户信息
    await db.collection('customers').doc(customerId).update({
      data: updateData
    })

    // 添加购买记录
    await db.collection('purchase_records').add({
      data: {
        customerId,
        productId: cardId,
        amount: paymentAmount || card.data.price,
        remark: `续费会员卡：${card.data.name}`,
        createTime: db.serverDate()
      }
    })

    return {
      success: true,
      message: '续费会员卡成功',
      data: {
        expireDate: expireDate,
        membershipLevel: membershipInfo.level
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '续费会员卡失败',
      error
    }
  }
}
