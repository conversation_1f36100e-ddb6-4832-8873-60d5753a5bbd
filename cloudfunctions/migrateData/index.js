// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action } = event
  
  switch (action) {
    case 'migrateMembershipCards':
      return migrateMembershipCards()
    case 'updateCustomerMembership':
      return updateCustomerMembership()
    default:
      return {
        success: false,
        message: '未知操作'
      }
  }
}

// 迁移会员卡数据到产品集合
async function migrateMembershipCards() {
  try {
    // 获取所有会员卡
    const cards = await db.collection('membership_cards').get()
    
    if (!cards.data || cards.data.length === 0) {
      return {
        success: true,
        message: '没有会员卡数据需要迁移'
      }
    }
    
    // 记录迁移结果
    const migrationResults = []
    
    // 迁移每张会员卡
    for (const card of cards.data) {
      try {
        // 构建会员卡产品数据
        const productData = {
          name: card.name,
          price: card.price,
          category: '会员卡',
          description: card.description || '',
          type: 'membership',
          membershipInfo: {
            duration: card.duration,
            trainingCount: card.trainingCount,
            level: card.type || 'basic',
            privileges: {
              discount: 0.9, // 默认9折
              pointsMultiplier: 1.0,
              freeShipping: false,
              priorityService: false
            },
            benefits: card.benefits || [],
            includedProducts: card.products || []
          },
          createTime: card.createTime,
          updateTime: db.serverDate(),
          originalCardId: card._id // 记录原始会员卡ID，用于关联
        }
        
        // 添加到产品集合
        const result = await db.collection('products').add({
          data: productData
        })
        
        migrationResults.push({
          originalId: card._id,
          newId: result._id,
          name: card.name,
          success: true
        })
      } catch (error) {
        migrationResults.push({
          originalId: card._id,
          name: card.name,
          success: false,
          error: error.message
        })
      }
    }
    
    return {
      success: true,
      message: `迁移完成，共迁移${migrationResults.length}张会员卡`,
      data: migrationResults
    }
  } catch (error) {
    return {
      success: false,
      message: '迁移会员卡数据失败',
      error
    }
  }
}

// 更新客户会员卡引用
async function updateCustomerMembership() {
  try {
    // 获取所有已迁移的会员卡产品
    const products = await db.collection('products')
      .where({
        type: 'membership',
        originalCardId: _.exists(true)
      })
      .get()
    
    if (!products.data || products.data.length === 0) {
      return {
        success: false,
        message: '没有找到已迁移的会员卡产品'
      }
    }
    
    // 创建映射表：原始会员卡ID -> 新产品ID
    const cardIdMap = {}
    products.data.forEach(product => {
      cardIdMap[product.originalCardId] = product._id
    })
    
    // 获取所有有会员卡的客户
    const customers = await db.collection('customers')
      .where({
        membershipCardId: _.exists(true)
      })
      .get()
    
    if (!customers.data || customers.data.length === 0) {
      return {
        success: true,
        message: '没有客户需要更新会员卡引用'
      }
    }
    
    // 记录更新结果
    const updateResults = []
    
    // 更新每个客户
    for (const customer of customers.data) {
      try {
        // 获取新的会员卡ID
        const newCardId = cardIdMap[customer.membershipCardId]
        
        if (!newCardId) {
          updateResults.push({
            customerId: customer._id,
            name: customer.name,
            success: false,
            message: '找不到对应的新会员卡ID'
          })
          continue
        }
        
        // 构建会员信息
        const membershipData = {
          cardId: newCardId,
          expireDate: customer.memberExpireDate,
          remainingTrainingCount: customer.remainingTrainingCount || 0,
          purchaseDate: customer.purchaseDate || db.serverDate(),
          renewalHistory: [{
            date: customer.purchaseDate || db.serverDate(),
            cardId: newCardId,
            amount: 0 // 无法获取原始支付金额
          }]
        }
        
        // 更新客户信息
        await db.collection('customers').doc(customer._id).update({
          data: {
            membership: membershipData,
            updateTime: db.serverDate()
          }
        })
        
        updateResults.push({
          customerId: customer._id,
          name: customer.name,
          success: true
        })
      } catch (error) {
        updateResults.push({
          customerId: customer._id,
          name: customer.name,
          success: false,
          error: error.message
        })
      }
    }
    
    return {
      success: true,
      message: `更新完成，共更新${updateResults.length}个客户`,
      data: updateResults
    }
  } catch (error) {
    return {
      success: false,
      message: '更新客户会员卡引用失败',
      error
    }
  }
}
