# 云函数部署说明

本项目包含以下云函数，需要部署到云端才能正常使用：

## 云函数列表

1. **initDatabase** - 初始化数据库
   - 功能：创建数据库集合并添加示例数据
   - 使用时机：首次使用小程序时，需要先初始化数据库

2. **customerService** - 客户管理服务
   - 功能：提供客户相关的增删改查功能
   - 接口：
     - getCustomerList：获取客户列表
     - getCustomerDetail：获取客户详情
     - addCustomer：添加客户
     - updateCustomer：更新客户
     - deleteCustomer：删除客户

3. **recordService** - 记录管理服务
   - 功能：提供训练记录和购买记录的增删改查功能
   - 接口：
     - addTrainingRecord：添加训练记录
     - getTrainingRecords：获取训练记录
     - addPurchaseRecord：添加购买记录
     - getPurchaseRecords：获取购买记录

4. **productService** - 产品管理服务
   - 功能：提供产品相关的增删改查功能
   - 接口：
     - getProductList：获取产品列表
     - getProductDetail：获取产品详情
     - addProduct：添加产品
     - updateProduct：更新产品
     - deleteProduct：删除产品

## 部署步骤

1. 打开微信开发者工具
2. 确保已经开通云开发服务
3. 在项目根目录右键点击 cloudfunctions 文件夹
4. 选择"上传并部署：云端安装依赖"
5. 在弹出的对话框中选择"云端安装依赖"
6. 等待部署完成

或者，您可以单独部署每个云函数：

1. 在 cloudfunctions 目录下，右键点击要部署的云函数文件夹
2. 选择"上传并部署：云端安装依赖"
3. 在弹出的对话框中选择"云端安装依赖"
4. 等待部署完成
5. 对每个云函数重复上述步骤

## 初始化数据库

部署完云函数后，需要初始化数据库：

1. 在微信开发者工具的云开发控制台中，选择"云函数"
2. 找到 initDatabase 云函数
3. 点击"测试"按钮
4. 等待执行完成
5. 检查云开发控制台的数据库中是否已创建以下集合：
   - customers
   - training_records
   - products
   - purchase_records

## 注意事项

- 每次修改云函数代码后，都需要重新部署
- 初始化数据库只需要执行一次，重复执行会导致数据重复
- 确保云函数的环境变量设置正确
