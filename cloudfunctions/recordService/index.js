// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event

  switch (action) {
    case 'addTrainingRecord':
      return addTrainingRecord(data)
    case 'getTrainingRecords':
      return getTrainingRecords(data)
    case 'getAllTrainingRecords':
      return getAllTrainingRecords(data)
    case 'addPurchaseRecord':
      return addPurchaseRecord(data)
    case 'getPurchaseRecords':
      return getPurchaseRecords(data)
    case 'getAllPurchaseRecords':
      return getAllPurchaseRecords(data)
    default:
      return {
        success: false,
        message: '未知操作'
      }
  }
}

// 添加训练记录
async function addTrainingRecord(data) {
  const { customerId } = data

  try {
    // 开始数据库事务
    const transaction = await db.startTransaction()

    try {
      // 添加训练记录
      const result = await transaction.collection('training_records').add({
        data: {
          ...data,
          createTime: db.serverDate()
        }
      })

      // 查询客户信息，检查是否需要扣减训练次数
      const customer = await transaction.collection('customers').doc(customerId).get()

      if (customer.data && customer.data.membershipCardId && customer.data.remainingTrainingCount > 0) {
        // 扣减训练次数
        await transaction.collection('customers').doc(customerId).update({
          data: {
            remainingTrainingCount: _.inc(-1),
            updateTime: db.serverDate()
          }
        })
      }

      // 提交事务
      await transaction.commit()

      return {
        success: true,
        message: '添加训练记录成功',
        data: {
          _id: result._id
        }
      }
    } catch (err) {
      // 回滚事务
      await transaction.rollback()
      throw err
    }
  } catch (error) {
    return {
      success: false,
      message: '添加训练记录失败',
      error
    }
  }
}

// 获取训练记录
async function getTrainingRecords(data) {
  const { customerId, page = 1, pageSize = 10 } = data

  try {
    // 构建查询条件
    let query = {}
    if (customerId) {
      query.customerId = customerId
    }

    // 计算总数
    const countResult = await db.collection('training_records').where(query).count()
    const total = countResult.total

    // 查询训练记录
    const records = await db.collection('training_records')
      .where(query)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    // 如果有客户ID，查询客户信息
    if (records.data.length > 0 && !customerId) {
      const customerIds = [...new Set(records.data.map(record => record.customerId))]

      const customersPromises = customerIds.map(async (id) => {
        const customer = await db.collection('customers').doc(id).get()
        return customer.data
      })

      const customers = await Promise.all(customersPromises)

      // 将客户信息合并到训练记录中
      records.data = records.data.map(record => {
        const customer = customers.find(c => c._id === record.customerId)
        return {
          ...record,
          customer
        }
      })
    }

    return {
      success: true,
      data: records.data,
      total,
      page,
      pageSize
    }
  } catch (error) {
    return {
      success: false,
      message: '获取训练记录失败',
      error
    }
  }
}

// 添加购买记录
async function addPurchaseRecord(data) {
  try {
    const result = await db.collection('purchase_records').add({
      data: {
        ...data,
        createTime: db.serverDate()
      }
    })

    return {
      success: true,
      message: '添加购买记录成功',
      data: {
        _id: result._id
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '添加购买记录失败',
      error
    }
  }
}

// 获取购买记录
async function getPurchaseRecords(data) {
  const { customerId, page = 1, pageSize = 10 } = data

  try {
    console.log('getPurchaseRecords 被调用，参数:', data)

    // 构建查询条件
    let query = {}
    if (customerId) {
      query.customerId = customerId
    }

    console.log('查询条件:', query)

    // 计算总数
    const countResult = await db.collection('purchase_records').where(query).count()
    const total = countResult.total

    console.log('购买记录总数:', total)

    // 查询购买记录
    const records = await db.collection('purchase_records')
      .where(query)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    console.log('查询到的购买记录:', records.data.length, '条')

    // 如果有客户ID，查询客户信息
    if (records.data.length > 0 && !customerId) {
      const customerIds = [...new Set(records.data.map(record => record.customerId))]

      const customersPromises = customerIds.map(async (id) => {
        const customer = await db.collection('customers').doc(id).get()
        return customer.data
      })

      const customers = await Promise.all(customersPromises)

      // 将客户信息合并到购买记录中
      records.data = records.data.map(record => {
        const customer = customers.find(c => c._id === record.customerId)
        return {
          ...record,
          customer
        }
      })
    }

    // 查询产品信息
    if (records.data.length > 0) {
      const productIds = [...new Set(records.data.map(record => record.productId))]

      const productsPromises = productIds.map(async (id) => {
        const product = await db.collection('products').doc(id).get()
        return product.data
      })

      const products = await Promise.all(productsPromises)

      // 将产品信息合并到购买记录中
      records.data = records.data.map(record => {
        const product = products.find(p => p._id === record.productId)
        return {
          ...record,
          product
        }
      })
    }

    return {
      success: true,
      data: records.data,
      total,
      page,
      pageSize
    }
  } catch (error) {
    return {
      success: false,
      message: '获取购买记录失败',
      error
    }
  }
}

// 获取所有训练记录（用于记录列表页）
async function getAllTrainingRecords(data) {
  const { page = 1, pageSize = 10 } = data

  try {
    // 查询训练记录
    const records = await db.collection('training_records')
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    // 查询客户信息
    if (records.data.length > 0) {
      const customerIds = [...new Set(records.data.map(record => record.customerId))]

      const customersPromises = customerIds.map(async (id) => {
        const customer = await db.collection('customers').doc(id).get()
        return customer.data
      })

      const customers = await Promise.all(customersPromises)

      // 将客户信息合并到训练记录中
      records.data = records.data.map(record => {
        const customer = customers.find(c => c._id === record.customerId)
        return {
          ...record,
          customer
        }
      })
    }

    return {
      success: true,
      data: records.data
    }
  } catch (error) {
    return {
      success: false,
      message: '获取训练记录失败',
      error
    }
  }
}

// 获取所有购买记录（用于记录列表页）
async function getAllPurchaseRecords(data) {
  const { page = 1, pageSize = 10 } = data

  try {
    // 查询购买记录
    const records = await db.collection('purchase_records')
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    // 查询客户信息
    if (records.data.length > 0) {
      const customerIds = [...new Set(records.data.map(record => record.customerId))]

      const customersPromises = customerIds.map(async (id) => {
        const customer = await db.collection('customers').doc(id).get()
        return customer.data
      })

      const customers = await Promise.all(customersPromises)

      // 将客户信息合并到购买记录中
      records.data = records.data.map(record => {
        const customer = customers.find(c => c._id === record.customerId)
        return {
          ...record,
          customer
        }
      })
    }

    // 查询产品信息
    if (records.data.length > 0) {
      const productIds = [...new Set(records.data.map(record => record.productId))]

      const productsPromises = productIds.map(async (id) => {
        const product = await db.collection('products').doc(id).get()
        return product.data
      })

      const products = await Promise.all(productsPromises)

      // 将产品信息合并到购买记录中
      records.data = records.data.map(record => {
        const product = products.find(p => p._id === record.productId)
        return {
          ...record,
          product
        }
      })
    }

    return {
      success: true,
      data: records.data
    }
  } catch (error) {
    return {
      success: false,
      message: '获取购买记录失败',
      error
    }
  }
}
