{"version": 3, "file": "bin.js", "sourceRoot": "", "sources": ["../src/bin.ts"], "names": [], "mappings": ";;;AAEA,+BAA6C;AAC7C,+BAAyC;AACzC,+BAA8B;AAC9B,iCAAiC;AACjC,2BAA2B;AAC3B,+BAAgC;AAChC,2BAA2B;AAC3B,2BAAyD;AACzD,2BAA4B;AAC5B,mCAAqE;AAErE;;GAEG;AACH,MAAM,aAAa,GAAG,WAAW,CAAA;AAEjC;;GAEG;AACH,MAAM,SAAS;IAMb,YAAoB,IAAY;QAAZ,SAAI,GAAJ,IAAI,CAAQ;QALhC,UAAK,GAAG,EAAE,CAAA;QACV,WAAM,GAAG,EAAE,CAAA;QACX,YAAO,GAAG,CAAC,CAAA;QACX,UAAK,GAAG,CAAC,CAAA;IAE0B,CAAC;CACrC;AAED;;GAEG;AACH,SAAgB,IAAI,CAAE,IAAc;IAClC,MAAM,IAAI,GAAG,GAAG,CAAC;QACf,wBAAwB;QACxB,QAAQ,EAAE,MAAM;QAChB,eAAe,EAAE,OAAO;QACxB,SAAS,EAAE,OAAO;QAClB,WAAW,EAAE,CAAC,MAAM,CAAC;QAErB,eAAe;QACf,QAAQ,EAAE,OAAO;QACjB,eAAe,EAAE,OAAO;QACxB,WAAW,EAAE,GAAG,CAAC,KAAK;QAEtB,mBAAmB;QACnB,OAAO,EAAE,MAAM;QACf,SAAS,EAAE,OAAO;QAClB,YAAY,EAAE,MAAM;QACpB,oBAAoB,EAAE,aAAK;QAC3B,WAAW,EAAE,MAAM;QACnB,sBAAsB,EAAE,CAAC,MAAM,CAAC;QAChC,UAAU,EAAE,CAAC,MAAM,CAAC;QACpB,kBAAkB,EAAE,OAAO;QAC3B,cAAc,EAAE,OAAO;QACvB,iBAAiB,EAAE,OAAO;QAC1B,UAAU,EAAE,OAAO;QACnB,gBAAgB,EAAE,OAAO;QACzB,eAAe,EAAE,OAAO;QACxB,kBAAkB,EAAE,OAAO;QAC3B,aAAa,EAAE,OAAO;QACtB,QAAQ,EAAE,OAAO;QAEjB,WAAW;QACX,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,eAAe;QACrB,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,eAAe;QACrB,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,kBAAkB;QACxB,IAAI,EAAE,iBAAiB;QACvB,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,sBAAsB;QAC5B,IAAI,EAAE,oBAAoB;KAC3B,EAAE;QACD,IAAI;QACJ,gBAAgB,EAAE,IAAI;KACvB,CAAC,CAAA;IAEF,+CAA+C;IAC/C,4EAA4E;IAC5E,YAAY;IACZ,MAAM,EACJ,OAAO,EAAE,GAAG,EACZ,QAAQ,EAAE,IAAI,GAAG,KAAK,EACtB,eAAe,EAAE,UAAU,GAAG,KAAK,EACnC,WAAW,EAAE,OAAO,GAAG,CAAC,EACxB,WAAW,EAAE,QAAQ,GAAG,EAAE,EAC1B,QAAQ,EAAE,IAAI,GAAG,SAAS,EAC1B,SAAS,EAAE,KAAK,GAAG,KAAK,EACxB,eAAe,EAAE,WAAW,GAAG,KAAK,EACpC,SAAS,EAAE,KAAK,EAChB,YAAY,EAAE,QAAQ,EACtB,oBAAoB,EAAE,eAAe,EACrC,WAAW,EAAE,OAAO,EACpB,sBAAsB,EAAE,iBAAiB,EACzC,UAAU,EAAE,MAAM,EAClB,kBAAkB,EAAE,aAAa,EACjC,cAAc,EAAE,SAAS,EACzB,iBAAiB,EAAE,YAAY,EAC/B,UAAU,EAAE,MAAM,EAClB,gBAAgB,EAAE,WAAW,EAC7B,eAAe,EAAE,UAAU,EAC3B,kBAAkB,EAAE,YAAY,EAChC,aAAa,EAAE,QAAQ,EACvB,QAAQ,EAAE,IAAI,EACf,GAAG,IAAI,CAAA;IAER,IAAI,IAAI,EAAE;QACR,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8Bb,CAAC,CAAA;QAEA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;IAED,8BAA8B;IAC9B,IAAI,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,CAAC,GAAG,CAAC,IAAI,eAAO,EAAE,CAAC,CAAA;QAC1B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;IAED,MAAM,GAAG,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAA;IAChC,wFAAwF;IACxF,MAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,cAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IACtE,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,UAAU,IAAI,WAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,CAAA;IAEnE,6CAA6C;IAC7C,MAAM,OAAO,GAAG,gBAAQ,CAAC;QACvB,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,UAAU,EAAE,UAAU,CAAC;QACxC,IAAI;QACJ,KAAK;QACL,MAAM;QACN,aAAa;QACb,SAAS;QACT,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,QAAQ;QACR,OAAO;QACP,WAAW;QACX,UAAU;QACV,QAAQ;QACR,iBAAiB;QACjB,eAAe;QACf,QAAQ,EAAE,IAAI,KAAK,SAAS;YAC1B,CAAC,CAAC,CAAC,IAAY,EAAE,EAAE;gBACjB,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;oBAAE,OAAO,KAAK,CAAC,KAAK,CAAA;gBAE3C,IAAI;oBACF,OAAO,iBAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;iBAClC;gBAAC,OAAO,GAAG,EAAE,EAAC,aAAa,EAAC;YAC/B,CAAC;YACD,CAAC,CAAC,SAAS;QACb,UAAU,EAAE,IAAI,KAAK,SAAS;YAC5B,CAAC,CAAC,CAAC,IAAY,EAAE,EAAE;gBACjB,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;oBAAE,OAAO,IAAI,CAAA;gBAEpC,IAAI;oBACF,MAAM,KAAK,GAAG,aAAQ,CAAC,IAAI,CAAC,CAAA;oBAC5B,OAAO,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,MAAM,EAAE,CAAA;iBACxC;gBAAC,OAAO,GAAG,EAAE;oBACZ,OAAO,KAAK,CAAA;iBACb;YACH,CAAC;YACD,CAAC,CAAC,SAAS;KACd,CAAC,CAAA;IAEF,8BAA8B;IAC9B,IAAI,OAAO,IAAI,CAAC,EAAE;QAChB,OAAO,CAAC,GAAG,CAAC,YAAY,eAAO,EAAE,CAAC,CAAA;QAClC,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;QACtC,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAA;QAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;IAED,iDAAiD;IACjD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACrC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAA;IAC5B,MAAM,CAAC,KAAK,GAAI,MAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAGnD;IAAC,MAAc,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;IAE1C,0DAA0D;IAC1D,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAA;IACnG,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAEjF,4DAA4D;IAC5D,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,WAAW,EAAE;QACtC,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;KACjD;SAAM;QACL,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;YACjB,MAAM,CAAC,OAAO,EAAE,CAAA;SACjB;aAAM;YACL,uEAAuE;YACvE,iCAAiC;YACjC,IAAI,WAAW,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE;gBACtC,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;aAChC;iBAAM;gBACL,IAAI,MAAM,GAAG,IAAI,IAAI,EAAE,CAAA;gBACvB,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,MAAM,IAAI,KAAK,CAAC,CAAA;gBAC5D,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAA;aAClF;SACF;KACF;AACH,CAAC;AA9MD,oBA8MC;AAED;;GAEG;AACH,SAAS,MAAM,CAAE,GAAY,EAAE,UAAoB,EAAE,UAAmB;IACtE,6CAA6C;IAC7C,IAAI,UAAU,EAAE;QACd,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,IAAI,SAAS,CAAC,4EAA4E,CAAC,CAAA;SAClG;QAED,IAAI,GAAG,EAAE;YACP,MAAM,IAAI,SAAS,CAAC,6CAA6C,CAAC,CAAA;SACnE;QAED,mEAAmE;QACnE,2FAA2F;QAC3F,wGAAwG;QACxG,sFAAsF;QACtF,6DAA6D;QAC7D,6EAA6E;QAC7E,yEAAyE;QACzE,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;QAC3C,MAAM,wBAAwB,GAAa,EAAE,CAAA;QAC7C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,EAAE,sBAAsB;gBACpE,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBAClC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,cAAY,CAAC,CAAA,CAAC,sBAAsB;aAC/D;SACF;QACD,IAAI;YACF,OAAO,cAAO,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAA;SAC5C;gBAAS;YACR,KAAK,MAAM,GAAG,IAAI,wBAAwB,EAAE;gBAC1C,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA,CAAC,sBAAsB;aACtD;SACF;KACF;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAE,OAAiB,EAAE,KAAgB,EAAE,MAAc,EAAE,IAAY,EAAE,SAAkB;IACzG,IAAI,MAAW,CAEd;IAAC,MAAc,CAAC,UAAU,GAAG,MAAM,CAAC,QAAQ,CAC5C;IAAC,MAAc,CAAC,SAAS,GAAG,cAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CACpD;IAAC,MAAc,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CACxC;IAAC,MAAc,CAAC,MAAM,GAAG,MAAM,CAC/B;IAAC,MAAc,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAEtD,IAAI;QACF,MAAM,GAAG,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;KACrC;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,eAAO,EAAE;YAC5B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;SAChB;QAED,MAAM,KAAK,CAAA;KACZ;IAED,IAAI,SAAS,EAAE;QACb,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,cAAO,CAAC,MAAM,CAAC,CAAC,CAAA;KACnE;AACH,CAAC;AAED;;GAEG;AACH,SAAS,KAAK,CAAE,OAAiB,EAAE,KAAgB,EAAE,KAAa;IAChE,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;IACzB,MAAM,YAAY,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACvC,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IACrC,IAAI,MAAc,CAAA;IAElB,IAAI;QACF,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAA;KAC1D;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,EAAE,CAAA;QACN,MAAM,GAAG,CAAA;KACV;IAED,qDAAqD;IACrD,MAAM,OAAO,GAAG,gBAAS,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAE/C,IAAI,YAAY,EAAE;QAChB,IAAI,EAAE,CAAA;KACP;SAAM;QACL,KAAK,CAAC,MAAM,GAAG,MAAM,CAAA;KACtB;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;QACvC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IAC/D,CAAC,EAAE,SAAS,CAAC,CAAA;AACf,CAAC;AAED;;GAEG;AACH,SAAS,IAAI,CAAE,IAAY,EAAE,QAAgB;IAC3C,MAAM,MAAM,GAAG,IAAI,WAAM,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;IAEvD,OAAO,MAAM,CAAC,gBAAgB,EAAE,CAAA;AAClC,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAE,OAAiB,EAAE,KAAgB,EAAE,IAAa;IACpE,6CAA6C;IAC7C,IAAI,IAAI,EAAE;QACR,IAAI;YACF,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI,CAAC,CAAA;SACnC;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAClB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;SAChB;KACF;IAED,MAAM,IAAI,GAAG,YAAK,CAAC;QACjB,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK;QAC9B,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE,IAAI;KAChB,CAAC,CAAA;IAEF;;OAEG;IACH,SAAS,QAAQ,CAAE,IAAY,EAAE,QAAa,EAAE,SAAiB,EAAE,QAAkD;QACnH,IAAI,GAAG,GAAiB,IAAI,CAAA;QAC5B,IAAI,MAAW,CAAA;QAEf,kDAAkD;QAClD,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,QAAQ,CAAC,GAAG,CAAC,CAAA;YACb,OAAM;SACP;QAED,IAAI;YACF,MAAM,GAAG,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;SACrC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,eAAO,EAAE;gBAC5B,oDAAoD;gBACpD,IAAI,kBAAW,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;oBACvC,GAAG,GAAG,IAAI,kBAAW,CAAC,KAAK,CAAC,CAAA;iBAC7B;qBAAM;oBACL,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;iBACrB;aACF;iBAAM;gBACL,GAAG,GAAG,KAAK,CAAA;aACZ;SACF;QAED,OAAO,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAC9B,CAAC;IAED,2DAA2D;IAC3D,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;IAEvC,SAAS,KAAK;QACZ,SAAS,EAAE,CAAA;QAEX,yEAAyE;QACzE,IAAI,CAAC,0BAA0B,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;IAC9C,CAAC;IAED,KAAK,EAAE,CAAA;IACP,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IAEvB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;QACzB,IAAI,EAAE,2CAA2C;QACjD,MAAM,EAAE,UAAU,UAAkB;YAClC,IAAI,CAAC,UAAU,EAAE;gBACf,IAAI,CAAC,aAAa,EAAE,CAAA;gBACpB,OAAM;aACP;YAED,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC1C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAE1F,IAAI,EAAE,CAAA;YAEN,IAAI,IAAI;gBAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,CAAA;YAC9C,IAAI,OAAO;gBAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,OAAO,IAAI,CAAC,CAAA;YACpD,IAAI,CAAC,aAAa,EAAE,CAAA;QACtB,CAAC;KACF,CAAC,CAAA;IAEF,iEAAiE;IACjE,IAAI,IAAI,CAAC,YAAY,EAAE;QACrB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,WAAI,CAAC,YAAO,EAAE,EAAE,uBAAuB,CAAC,CAAA;QAE3F,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;YACnC,IAAI,CAAC,GAAG;gBAAE,OAAM;YAEhB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAClB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC,CAAC,CAAA;KACH;AACH,CAAC;AAED;;GAEG;AACH,SAAS,UAAU,CAAE,KAAgB,EAAE,KAAa;IAClD,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAA;IAC7B,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAA;IACjC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAA;IAC/B,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAA;IAE7B,mDAAmD;IACnD,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC9G,KAAK,CAAC,KAAK,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAA;KAC/C;IAED,KAAK,CAAC,KAAK,IAAI,KAAK,CAAA;IACpB,KAAK,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC,CAAA;IAC/B,KAAK,CAAC,OAAO,EAAE,CAAA;IAEf,OAAO;QACL,KAAK,CAAC,KAAK,GAAG,SAAS,CAAA;QACvB,KAAK,CAAC,MAAM,GAAG,UAAU,CAAA;QACzB,KAAK,CAAC,OAAO,GAAG,WAAW,CAAA;QAC3B,KAAK,CAAC,KAAK,GAAG,SAAS,CAAA;IACzB,CAAC,CAAA;AACH,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAE,KAAa;IAC/B,IAAI,KAAK,GAAG,CAAC,CAAA;IAEb,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,KAAK,EAAE,CAAA;SACR;KACF;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,MAAM,cAAc,GAAgB,IAAI,GAAG,CAAC;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,CAAC,oFAAoF;CAC1F,CAAC,CAAA;AAEF;;GAEG;AACH,SAAS,aAAa,CAAE,KAAc;IACpC,OAAO,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;AACtE,CAAC;AAED,4BAA4B;AAC5B,SAAS,cAAc,CAAE,MAAW,EAAE,QAAgB;IACpD,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;AAC/D,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;IAC3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CAC5B", "sourcesContent": ["#!/usr/bin/env node\n\nimport { join, resolve, dirname } from 'path'\nimport { start, Recoverable } from 'repl'\nimport { inspect } from 'util'\nimport Module = require('module')\nimport arg = require('arg')\nimport { diffLines } from 'diff'\nimport { Script } from 'vm'\nimport { readFileSync, statSync, realpathSync } from 'fs'\nimport { homedir } from 'os'\nimport { VERSION, TSError, parse, Register, register } from './index'\n\n/**\n * Eval filename for REPL/debug.\n */\nconst EVAL_FILENAME = `[eval].ts`\n\n/**\n * Eval state management.\n */\nclass EvalState {\n  input = ''\n  output = ''\n  version = 0\n  lines = 0\n\n  constructor (public path: string) {}\n}\n\n/**\n * Main `bin` functionality.\n */\nexport function main (argv: string[]) {\n  const args = arg({\n    // Node.js-like options.\n    '--eval': String,\n    '--interactive': Boolean,\n    '--print': <PERSON><PERSON>an,\n    '--require': [String],\n\n    // CLI options.\n    '--help': <PERSON><PERSON><PERSON>,\n    '--script-mode': <PERSON><PERSON><PERSON>,\n    '--version': arg.COUNT,\n\n    // Project options.\n    '--dir': String,\n    '--files': <PERSON><PERSON><PERSON>,\n    '--compiler': String,\n    '--compiler-options': parse,\n    '--project': String,\n    '--ignore-diagnostics': [String],\n    '--ignore': [String],\n    '--transpile-only': Boolean,\n    '--type-check': Boolean,\n    '--compiler-host': Boolean,\n    '--pretty': Boolean,\n    '--skip-project': Boolean,\n    '--skip-ignore': Boolean,\n    '--prefer-ts-exts': Boolean,\n    '--log-error': Boolean,\n    '--emit': Boolean,\n\n    // Aliases.\n    '-e': '--eval',\n    '-i': '--interactive',\n    '-p': '--print',\n    '-r': '--require',\n    '-h': '--help',\n    '-s': '--script-mode',\n    '-v': '--version',\n    '-T': '--transpile-only',\n    '-H': '--compiler-host',\n    '-I': '--ignore',\n    '-P': '--project',\n    '-C': '--compiler',\n    '-D': '--ignore-diagnostics',\n    '-O': '--compiler-options'\n  }, {\n    argv,\n    stopAtPositional: true\n  })\n\n  // Only setting defaults for CLI-specific flags\n  // Anything passed to `register()` can be `undefined`; `create()` will apply\n  // defaults.\n  const {\n    '--dir': dir,\n    '--help': help = false,\n    '--script-mode': scriptMode = false,\n    '--version': version = 0,\n    '--require': requires = [],\n    '--eval': code = undefined,\n    '--print': print = false,\n    '--interactive': interactive = false,\n    '--files': files,\n    '--compiler': compiler,\n    '--compiler-options': compilerOptions,\n    '--project': project,\n    '--ignore-diagnostics': ignoreDiagnostics,\n    '--ignore': ignore,\n    '--transpile-only': transpileOnly,\n    '--type-check': typeCheck,\n    '--compiler-host': compilerHost,\n    '--pretty': pretty,\n    '--skip-project': skipProject,\n    '--skip-ignore': skipIgnore,\n    '--prefer-ts-exts': preferTsExts,\n    '--log-error': logError,\n    '--emit': emit\n  } = args\n\n  if (help) {\n    console.log(`\n  Usage: ts-node [options] [ -e script | script.ts ] [arguments]\n\n  Options:\n\n    -e, --eval [code]              Evaluate code\n    -p, --print                    Print result of \\`--eval\\`\n    -r, --require [path]           Require a node module before execution\n    -i, --interactive              Opens the REPL even if stdin does not appear to be a terminal\n\n    -h, --help                     Print CLI usage\n    -v, --version                  Print module version information\n    -s, --script-mode              Use cwd from <script.ts> instead of current directory\n\n    -T, --transpile-only           Use TypeScript's faster \\`transpileModule\\`\n    -H, --compiler-host            Use TypeScript's compiler host API\n    -I, --ignore [pattern]         Override the path patterns to skip compilation\n    -P, --project [path]           Path to TypeScript JSON project file\n    -C, --compiler [name]          Specify a custom TypeScript compiler\n    -D, --ignore-diagnostics [code] Ignore TypeScript warnings by diagnostic code\n    -O, --compiler-options [opts]   JSON object to merge with compiler options\n\n    --dir                          Specify working directory for config resolution\n    --scope                        Scope compiler to files within \\`cwd\\` only\n    --files                        Load \\`files\\`, \\`include\\` and \\`exclude\\` from \\`tsconfig.json\\` on startup\n    --pretty                       Use pretty diagnostic formatter (usually enabled by default)\n    --skip-project                 Skip reading \\`tsconfig.json\\`\n    --skip-ignore                  Skip \\`--ignore\\` checks\n    --prefer-ts-exts               Prefer importing TypeScript files over JavaScript files\n    --log-error                    Logs TypeScript errors to stderr instead of throwing exceptions\n  `)\n\n    process.exit(0)\n  }\n\n  // Output project information.\n  if (version === 1) {\n    console.log(`v${VERSION}`)\n    process.exit(0)\n  }\n\n  const cwd = dir || process.cwd()\n  /** Unresolved.  May point to a symlink, not realpath.  May be missing file extension */\n  const scriptPath = args._.length ? resolve(cwd, args._[0]) : undefined\n  const state = new EvalState(scriptPath || join(cwd, EVAL_FILENAME))\n\n  // Register the TypeScript compiler instance.\n  const service = register({\n    dir: getCwd(dir, scriptMode, scriptPath),\n    emit,\n    files,\n    pretty,\n    transpileOnly,\n    typeCheck,\n    compilerHost,\n    ignore,\n    preferTsExts,\n    logError,\n    project,\n    skipProject,\n    skipIgnore,\n    compiler,\n    ignoreDiagnostics,\n    compilerOptions,\n    readFile: code !== undefined\n      ? (path: string) => {\n        if (path === state.path) return state.input\n\n        try {\n          return readFileSync(path, 'utf8')\n        } catch (err) {/* Ignore. */}\n      }\n      : undefined,\n    fileExists: code !== undefined\n      ? (path: string) => {\n        if (path === state.path) return true\n\n        try {\n          const stats = statSync(path)\n          return stats.isFile() || stats.isFIFO()\n        } catch (err) {\n          return false\n        }\n      }\n      : undefined\n  })\n\n  // Output project information.\n  if (version >= 2) {\n    console.log(`ts-node v${VERSION}`)\n    console.log(`node ${process.version}`)\n    console.log(`compiler v${service.ts.version}`)\n    process.exit(0)\n  }\n\n  // Create a local module instance based on `cwd`.\n  const module = new Module(state.path)\n  module.filename = state.path\n  module.paths = (Module as any)._nodeModulePaths(cwd)\n\n  // Require specified modules before start-up.\n  ;(Module as any)._preloadModules(requires)\n\n  // Prepend `ts-node` arguments to CLI for child processes.\n  process.execArgv.unshift(__filename, ...process.argv.slice(2, process.argv.length - args._.length))\n  process.argv = [process.argv[1]].concat(scriptPath || []).concat(args._.slice(1))\n\n  // Execute the main contents (either eval, script or piped).\n  if (code !== undefined && !interactive) {\n    evalAndExit(service, state, module, code, print)\n  } else {\n    if (args._.length) {\n      Module.runMain()\n    } else {\n      // Piping of execution _only_ occurs when no other script is specified.\n      // --interactive flag forces REPL\n      if (interactive || process.stdin.isTTY) {\n        startRepl(service, state, code)\n      } else {\n        let buffer = code || ''\n        process.stdin.on('data', (chunk: Buffer) => buffer += chunk)\n        process.stdin.on('end', () => evalAndExit(service, state, module, buffer, print))\n      }\n    }\n  }\n}\n\n/**\n * Get project path from args.\n */\nfunction getCwd (dir?: string, scriptMode?: boolean, scriptPath?: string) {\n  // Validate `--script-mode` usage is correct.\n  if (scriptMode) {\n    if (!scriptPath) {\n      throw new TypeError('Script mode must be used with a script name, e.g. `ts-node -s <script.ts>`')\n    }\n\n    if (dir) {\n      throw new TypeError('Script mode cannot be combined with `--dir`')\n    }\n\n    // Use node's own resolution behavior to ensure we follow symlinks.\n    // scriptPath may omit file extension or point to a directory with or without package.json.\n    // This happens before we are registered, so we tell node's resolver to consider ts, tsx, and jsx files.\n    // In extremely rare cases, is is technically possible to resolve the wrong directory,\n    // because we do not yet know preferTsExts, jsx, nor allowJs.\n    // See also, justification why this will not happen in real-world situations:\n    // https://github.com/TypeStrong/ts-node/pull/1009#issuecomment-613017081\n    const exts = ['.js', '.jsx', '.ts', '.tsx']\n    const extsTemporarilyInstalled: string[] = []\n    for (const ext of exts) {\n      if (!hasOwnProperty(require.extensions, ext)) { // tslint:disable-line\n        extsTemporarilyInstalled.push(ext)\n        require.extensions[ext] = function() {} // tslint:disable-line\n      }\n    }\n    try {\n      return dirname(require.resolve(scriptPath))\n    } finally {\n      for (const ext of extsTemporarilyInstalled) {\n        delete require.extensions[ext] // tslint:disable-line\n      }\n    }\n  }\n\n  return dir\n}\n\n/**\n * Evaluate a script.\n */\nfunction evalAndExit (service: Register, state: EvalState, module: Module, code: string, isPrinted: boolean) {\n  let result: any\n\n  ;(global as any).__filename = module.filename\n  ;(global as any).__dirname = dirname(module.filename)\n  ;(global as any).exports = module.exports\n  ;(global as any).module = module\n  ;(global as any).require = module.require.bind(module)\n\n  try {\n    result = _eval(service, state, code)\n  } catch (error) {\n    if (error instanceof TSError) {\n      console.error(error)\n      process.exit(1)\n    }\n\n    throw error\n  }\n\n  if (isPrinted) {\n    console.log(typeof result === 'string' ? result : inspect(result))\n  }\n}\n\n/**\n * Evaluate the code snippet.\n */\nfunction _eval (service: Register, state: EvalState, input: string) {\n  const lines = state.lines\n  const isCompletion = !/\\n$/.test(input)\n  const undo = appendEval(state, input)\n  let output: string\n\n  try {\n    output = service.compile(state.input, state.path, -lines)\n  } catch (err) {\n    undo()\n    throw err\n  }\n\n  // Use `diff` to check for new JavaScript to execute.\n  const changes = diffLines(state.output, output)\n\n  if (isCompletion) {\n    undo()\n  } else {\n    state.output = output\n  }\n\n  return changes.reduce((result, change) => {\n    return change.added ? exec(change.value, state.path) : result\n  }, undefined)\n}\n\n/**\n * Execute some code.\n */\nfunction exec (code: string, filename: string) {\n  const script = new Script(code, { filename: filename })\n\n  return script.runInThisContext()\n}\n\n/**\n * Start a CLI REPL.\n */\nfunction startRepl (service: Register, state: EvalState, code?: string) {\n  // Eval incoming code before the REPL starts.\n  if (code) {\n    try {\n      _eval(service, state, `${code}\\n`)\n    } catch (err) {\n      console.error(err)\n      process.exit(1)\n    }\n  }\n\n  const repl = start({\n    prompt: '> ',\n    input: process.stdin,\n    output: process.stdout,\n    terminal: process.stdout.isTTY,\n    eval: replEval,\n    useGlobal: true\n  })\n\n  /**\n   * Eval code from the REPL.\n   */\n  function replEval (code: string, _context: any, _filename: string, callback: (err: Error | null, result?: any) => any) {\n    let err: Error | null = null\n    let result: any\n\n    // TODO: Figure out how to handle completion here.\n    if (code === '.scope') {\n      callback(err)\n      return\n    }\n\n    try {\n      result = _eval(service, state, code)\n    } catch (error) {\n      if (error instanceof TSError) {\n        // Support recoverable compilations using >= node 6.\n        if (Recoverable && isRecoverable(error)) {\n          err = new Recoverable(error)\n        } else {\n          console.error(error)\n        }\n      } else {\n        err = error\n      }\n    }\n\n    return callback(err, result)\n  }\n\n  // Bookmark the point where we should reset the REPL state.\n  const resetEval = appendEval(state, '')\n\n  function reset () {\n    resetEval()\n\n    // Hard fix for TypeScript forcing `Object.defineProperty(exports, ...)`.\n    exec('exports = module.exports', state.path)\n  }\n\n  reset()\n  repl.on('reset', reset)\n\n  repl.defineCommand('type', {\n    help: 'Check the type of a TypeScript identifier',\n    action: function (identifier: string) {\n      if (!identifier) {\n        repl.displayPrompt()\n        return\n      }\n\n      const undo = appendEval(state, identifier)\n      const { name, comment } = service.getTypeInfo(state.input, state.path, state.input.length)\n\n      undo()\n\n      if (name) repl.outputStream.write(`${name}\\n`)\n      if (comment) repl.outputStream.write(`${comment}\\n`)\n      repl.displayPrompt()\n    }\n  })\n\n  // Set up REPL history when available natively via node.js >= 11.\n  if (repl.setupHistory) {\n    const historyPath = process.env.TS_NODE_HISTORY || join(homedir(), '.ts_node_repl_history')\n\n    repl.setupHistory(historyPath, err => {\n      if (!err) return\n\n      console.error(err)\n      process.exit(1)\n    })\n  }\n}\n\n/**\n * Append to the eval instance and return an undo function.\n */\nfunction appendEval (state: EvalState, input: string) {\n  const undoInput = state.input\n  const undoVersion = state.version\n  const undoOutput = state.output\n  const undoLines = state.lines\n\n  // Handle ASI issues with TypeScript re-evaluation.\n  if (undoInput.charAt(undoInput.length - 1) === '\\n' && /^\\s*[\\/\\[(`-]/.test(input) && !/;\\s*$/.test(undoInput)) {\n    state.input = `${state.input.slice(0, -1)};\\n`\n  }\n\n  state.input += input\n  state.lines += lineCount(input)\n  state.version++\n\n  return function () {\n    state.input = undoInput\n    state.output = undoOutput\n    state.version = undoVersion\n    state.lines = undoLines\n  }\n}\n\n/**\n * Count the number of lines.\n */\nfunction lineCount (value: string) {\n  let count = 0\n\n  for (const char of value) {\n    if (char === '\\n') {\n      count++\n    }\n  }\n\n  return count\n}\n\nconst RECOVERY_CODES: Set<number> = new Set([\n  1003, // \"Identifier expected.\"\n  1005, // \"')' expected.\"\n  1109, // \"Expression expected.\"\n  1126, // \"Unexpected end of text.\"\n  1160, // \"Unterminated template literal.\"\n  1161, // \"Unterminated regular expression literal.\"\n  2355 // \"A function whose declared type is neither 'void' nor 'any' must return a value.\"\n])\n\n/**\n * Check if a function can recover gracefully.\n */\nfunction isRecoverable (error: TSError) {\n  return error.diagnosticCodes.every(code => RECOVERY_CODES.has(code))\n}\n\n/** Safe `hasOwnProperty` */\nfunction hasOwnProperty (object: any, property: string): boolean {\n  return Object.prototype.hasOwnProperty.call(object, property)\n}\n\nif (require.main === module) {\n  main(process.argv.slice(2))\n}\n"]}