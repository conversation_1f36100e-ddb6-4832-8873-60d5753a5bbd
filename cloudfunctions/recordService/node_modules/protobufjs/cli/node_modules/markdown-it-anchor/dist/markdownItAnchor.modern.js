let e=!1;const n={false:"push",true:"unshift",after:"push",before:"unshift"},t={isPermalinkSymbol:!0};function i(i,r,a,l){if(!e){const n="Using deprecated markdown-it-anchor permalink option, see https://github.com/valeriangalliat/markdown-it-anchor#todo-anchor-or-file";"object"==typeof process&&process&&process.emitWarning?process.emitWarning(n):console.warn(n),e=!0}const o=[Object.assign(new a.Token("link_open","a",1),{attrs:[...r.permalinkClass?[["class",r.permalinkClass]]:[],["href",r.permalinkHref(i,a)],...Object.entries(r.permalinkAttrs(i,a))]}),Object.assign(new a.Token("html_block","",0),{content:r.permalinkSymbol,meta:t}),new a.Token("link_close","a",-1)];r.permalinkSpace&&a.tokens[l+1].children[n[r.permalinkBefore]](Object.assign(new a.Token("text","",0),{content:" "})),a.tokens[l+1].children[n[r.permalinkBefore]](...o)}function r(e){return"#"+e}function a(e){return{}}const l={class:"header-anchor",symbol:"#",renderHref:r,renderAttrs:a};function o(e){function n(t){return t=Object.assign({},n.defaults,t),(n,i,r,a)=>e(n,t,i,r,a)}return n.defaults=Object.assign({},l),n.renderPermalinkImpl=e,n}const c=o((e,i,r,a,l)=>{const o=[Object.assign(new a.Token("link_open","a",1),{attrs:[...i.class?[["class",i.class]]:[],["href",i.renderHref(e,a)],...i.ariaHidden?[["aria-hidden","true"]]:[],...Object.entries(i.renderAttrs(e,a))]}),Object.assign(new a.Token("html_inline","",0),{content:i.symbol,meta:t}),new a.Token("link_close","a",-1)];if(i.space){const e="string"==typeof i.space?i.space:" ";a.tokens[l+1].children[n[i.placement]](Object.assign(new a.Token("string"==typeof i.space?"html_inline":"text","",0),{content:e}))}a.tokens[l+1].children[n[i.placement]](...o)});Object.assign(c.defaults,{space:!0,placement:"after",ariaHidden:!1});const s=o(c.renderPermalinkImpl);s.defaults=Object.assign({},c.defaults,{ariaHidden:!0});const d=o((e,n,t,i,r)=>{const a=[Object.assign(new i.Token("link_open","a",1),{attrs:[...n.class?[["class",n.class]]:[],["href",n.renderHref(e,i)],...Object.entries(n.renderAttrs(e,i))]}),...n.safariReaderFix?[new i.Token("span_open","span",1)]:[],...i.tokens[r+1].children,...n.safariReaderFix?[new i.Token("span_close","span",-1)]:[],new i.Token("link_close","a",-1)];i.tokens[r+1]=Object.assign(new i.Token("inline","",0),{children:a})});Object.assign(d.defaults,{safariReaderFix:!1});const b=o((e,i,r,a,l)=>{if(!["visually-hidden","aria-label","aria-describedby","aria-labelledby"].includes(i.style))throw new Error(`\`permalink.linkAfterHeader\` called with unknown style option \`${i.style}\``);if(!["aria-describedby","aria-labelledby"].includes(i.style)&&!i.assistiveText)throw new Error(`\`permalink.linkAfterHeader\` called without the \`assistiveText\` option in \`${i.style}\` style`);if("visually-hidden"===i.style&&!i.visuallyHiddenClass)throw new Error("`permalink.linkAfterHeader` called without the `visuallyHiddenClass` option in `visually-hidden` style");const o=a.tokens[l+1].children.filter(e=>"text"===e.type||"code_inline"===e.type).reduce((e,n)=>e+n.content,""),c=[],s=[];if(i.class&&s.push(["class",i.class]),s.push(["href",i.renderHref(e,a)]),s.push(...Object.entries(i.renderAttrs(e,a))),"visually-hidden"===i.style){if(c.push(Object.assign(new a.Token("span_open","span",1),{attrs:[["class",i.visuallyHiddenClass]]}),Object.assign(new a.Token("text","",0),{content:i.assistiveText(o)}),new a.Token("span_close","span",-1)),i.space){const e="string"==typeof i.space?i.space:" ";c[n[i.placement]](Object.assign(new a.Token("string"==typeof i.space?"html_inline":"text","",0),{content:e}))}c[n[i.placement]](Object.assign(new a.Token("span_open","span",1),{attrs:[["aria-hidden","true"]]}),Object.assign(new a.Token("html_inline","",0),{content:i.symbol,meta:t}),new a.Token("span_close","span",-1))}else c.push(Object.assign(new a.Token("html_inline","",0),{content:i.symbol,meta:t}));"aria-label"===i.style?s.push(["aria-label",i.assistiveText(o)]):["aria-describedby","aria-labelledby"].includes(i.style)&&s.push([i.style,e]);const d=[Object.assign(new a.Token("link_open","a",1),{attrs:s}),...c,new a.Token("link_close","a",-1)];a.tokens.splice(l+3,0,...d),i.wrapper&&(a.tokens.splice(l,0,Object.assign(new a.Token("html_block","",0),{content:i.wrapper[0]+"\n"})),a.tokens.splice(l+3+d.length+1,0,Object.assign(new a.Token("html_block","",0),{content:i.wrapper[1]+"\n"})))});function p(e,n,t,i){let r=e,a=i;if(t&&Object.prototype.hasOwnProperty.call(n,r))throw new Error(`User defined \`id\` attribute \`${e}\` is not unique. Please fix it in your Markdown to continue.`);for(;Object.prototype.hasOwnProperty.call(n,r);)r=`${e}-${a}`,a+=1;return n[r]=!0,r}function f(e,n){n=Object.assign({},f.defaults,n),e.core.ruler.push("anchor",e=>{const t={},r=e.tokens,a=Array.isArray(n.level)?(l=n.level,e=>l.includes(e)):(e=>n=>n>=e)(n.level);var l;for(let l=0;l<r.length;l++){const o=r[l];if("heading_open"!==o.type)continue;if(!a(Number(o.tag.substr(1))))continue;const c=n.getTokensText(r[l+1].children);let s=o.attrGet("id");s=null==s?p(n.slugify(c),t,!1,n.uniqueSlugStartIndex):p(s,t,!0,n.uniqueSlugStartIndex),o.attrSet("id",s),!1!==n.tabIndex&&o.attrSet("tabindex",""+n.tabIndex),"function"==typeof n.permalink?n.permalink(s,n,e,l):(n.permalink||n.renderPermalink&&n.renderPermalink!==i)&&n.renderPermalink(s,n,e,l),l=r.indexOf(o),n.callback&&n.callback(o,{slug:s,title:c})}})}Object.assign(b.defaults,{style:"visually-hidden",space:!0,placement:"after",wrapper:null}),f.permalink={__proto__:null,legacy:i,renderHref:r,renderAttrs:a,makePermalink:o,linkInsideHeader:c,ariaHidden:s,headerLink:d,linkAfterHeader:b},f.defaults={level:1,slugify:e=>encodeURIComponent(String(e).trim().toLowerCase().replace(/\s+/g,"-")),uniqueSlugStartIndex:1,tabIndex:"-1",getTokensText:function(e){return e.filter(e=>["text","code_inline"].includes(e.type)).map(e=>e.content).join("")},permalink:!1,renderPermalink:i,permalinkClass:s.defaults.class,permalinkSpace:s.defaults.space,permalinkSymbol:"¶",permalinkBefore:"before"===s.defaults.placement,permalinkHref:s.defaults.renderHref,permalinkAttrs:s.defaults.renderAttrs},f.default=f;export default f;
//# sourceMappingURL=markdownItAnchor.modern.js.map
