// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const results = []

    // 创建集合的辅助函数
    const createCollectionIfNotExists = async (collectionName) => {
      try {
        await db.createCollection(collectionName)
        results.push(`${collectionName} 集合创建成功`)
      } catch (error) {
        if (error.errCode === -501001) {
          // 集合已存在
          results.push(`${collectionName} 集合已存在`)
        } else {
          throw error
        }
      }
    }

    // 创建所有需要的集合
    await createCollectionIfNotExists('customers')
    await createCollectionIfNotExists('training_records')
    await createCollectionIfNotExists('products')
    await createCollectionIfNotExists('purchase_records')
    await createCollectionIfNotExists('membership_cards')
    await createCollectionIfNotExists('categories')

    // 添加示例数据的辅助函数
    const addSampleDataIfEmpty = async (collectionName, sampleData, dataName) => {
      try {
        const count = await db.collection(collectionName).count()
        if (count.total === 0) {
          await db.collection(collectionName).add({
            data: sampleData
          })
          results.push(`${dataName} 示例数据添加成功`)
        } else {
          results.push(`${dataName} 数据已存在，跳过添加`)
        }
      } catch (error) {
        results.push(`${dataName} 数据添加失败: ${error.message}`)
      }
    }

    // 添加示例产品
    const productsData = [
      {
        name: '眼镜A型',
        price: 1200,
        description: '高端矫正眼镜',
        category: '眼镜',
        createTime: db.serverDate()
      },
      {
        name: '眼镜B型',
        price: 980,
        description: '标准矫正眼镜',
        category: '眼镜',
        createTime: db.serverDate()
      },
      {
        name: '视力训练套装',
        price: 580,
        description: '家用视力训练工具套装',
        category: '训练工具',
        createTime: db.serverDate()
      }
    ]

    await addSampleDataIfEmpty('products', productsData, '产品')

    // 添加示例客户
    const customersData = [
      {
        name: '张三',
        phone: '13800138000',
        joinDate: db.serverDate(),
        memberExpireDate: new Date('2023-12-31'),
        notes: '近视500度',
        createTime: db.serverDate()
      },
      {
        name: '李四',
        phone: '13900139000',
        joinDate: db.serverDate(),
        memberExpireDate: new Date('2023-10-15'),
        notes: '散光100度',
        createTime: db.serverDate()
      }
    ]

    await addSampleDataIfEmpty('customers', customersData, '客户')

    // 添加示例会员卡
    const membershipCardsData = [
      {
        name: '基础会员卡',
        type: 'basic', // 会员卡类型
        duration: 3, // 3个月
        price: 1000,
        trainingCount: 12, // 12次训练
        products: [],
        benefits: [
          {
            name: '基础训练',
            count: 12,
            description: '标准视力训练课程'
          }
        ],
        description: '包含12次训练，有效期3个月',
        createTime: db.serverDate()
      },
      {
        name: '高级会员卡',
        type: 'premium', // 会员卡类型
        duration: 6, // 6个月
        price: 2000,
        trainingCount: 30, // 30次训练
        products: [],
        benefits: [
          {
            name: '标准训练',
            count: 20,
            description: '标准视力训练课程'
          },
          {
            name: '高级训练',
            count: 10,
            description: '高级视力训练课程'
          }
        ],
        description: '包含30次训练，有效期6个月',
        createTime: db.serverDate()
      },
      {
        name: '年度会员卡',
        type: 'annual', // 会员卡类型
        duration: 12, // 12个月
        price: 3600,
        trainingCount: 60, // 60次训练
        products: [],
        benefits: [
          {
            name: '标准训练',
            count: 30,
            description: '标准视力训练课程'
          },
          {
            name: '高级训练',
            count: 20,
            description: '高级视力训练课程'
          },
          {
            name: '专家训练',
            count: 10,
            description: '专家一对一训练'
          }
        ],
        description: '包含60次训练，有效期12个月',
        createTime: db.serverDate()
      }
    ]

    await addSampleDataIfEmpty('membership_cards', membershipCardsData, '会员卡')

    // 添加示例分类
    const categoriesData = [
      {
        name: '眼镜',
        type: 'normal', // 普通产品分类
        description: '各类矫正眼镜',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '训练工具',
        type: 'normal', // 普通产品分类
        description: '视力训练相关工具',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '保健品',
        type: 'normal', // 普通产品分类
        description: '眼睛保健相关产品',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '其他',
        type: 'normal', // 普通产品分类
        description: '其他类型产品',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '基础会员卡',
        type: 'membership', // 会员卡分类
        description: '基础级别会员卡',
        level: 'basic',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '高级会员卡',
        type: 'membership', // 会员卡分类
        description: '高级级别会员卡',
        level: 'premium',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '年度会员卡',
        type: 'membership', // 会员卡分类
        description: '年度级别会员卡',
        level: 'annual',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      },
      {
        name: '定制会员卡',
        type: 'membership', // 会员卡分类
        description: '定制级别会员卡',
        level: 'custom',
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    ]

    await addSampleDataIfEmpty('categories', categoriesData, '分类')

    return {
      success: true,
      message: '数据库初始化成功',
      details: results
    }
  } catch (error) {
    return {
      success: false,
      message: '数据库初始化失败',
      error
    }
  }
}
